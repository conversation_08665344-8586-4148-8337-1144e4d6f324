import dayjs from 'dayjs';

export function getFilterText(column_config, filter_config) {
  // console.log(':::getFilterText::', column_config, filter_config);
  const column_label = column_config.alias;
  if (!filter_config) {
    return column_label;
  }

  if (filter_config.operator.valueType === 'none') {
    return `${column_label} ${filter_config.operator.label}`;
  }
  else if (['between', 'not_between'].includes(filter_config.operator.name) && !Array.isArray(filter_config?.operator_value)) {
    return `${column_label} is ${filter_config.operator.label} ${filter_config.operator_value_min} and ${filter_config.operator_value_max}`;
  }
  else if (['between', 'not_between'].includes(filter_config.operator.name) && Array.isArray(filter_config.operator_value)) {
    return `${column_label} is ${filter_config.operator.label} ${filter_config.operator_value[0]} and ${filter_config.operator_value[1]}`;
  }
  else if (['equals', 'not_equals', 'greater_than', 'less_than'].includes(filter_config.operator.name)) {
    return `${column_label} is ${filter_config.operator.label} ${Array.isArray(filter_config.operator_value) ? `${filter_config.operator_value.length} values` : filter_config.operator_value}`;
  }
  else if (filter_config.operator.name === 'in') {
    return `${column_label} is ${filter_config.operator_value.length} selection/s`;
  }
  else if (filter_config.operator.name === 'not_in') {
    return `${column_label} is not ${filter_config.operator_value.length} selection/s`;
  }
  else if (['contains', 'does_not_contain', 'starts_with', 'ends_with'].includes(filter_config.operator.name)) {
    return `${column_label} ${filter_config.operator.label} ${filter_config.operator_value}`;
  }
  else {
    return column_label;
  }
}

export function generateNtoDateOperatorVal(config) {
  const { anchor_date_starting_amount, anchor_date_starting_custom_date, anchor_date_starting_point, anchor_date_starting_time_unit } = config;

  let parsed_string;

  if (anchor_date_starting_point === 'after') {
    parsed_string = `${anchor_date_starting_amount}_${anchor_date_starting_time_unit}`;
  }
  if (anchor_date_starting_point === 'before') {
    parsed_string = `-${anchor_date_starting_amount}_${anchor_date_starting_time_unit}`;
  }
  if (anchor_date_starting_point === 'custom_date') {
    parsed_string = dayjs(anchor_date_starting_custom_date).format('YYYY-MM-DD');
  }

  return parsed_string;
}

export function generateRelativeToDateOperatorVal(config) {
  const { anchor_period_starting_amount, anchor_period_starting_custom_date, anchor_period_starting_point, anchor_period_starting_time_unit, anchor_period_time_unit } = config;

  let query_string;
  let anchor_string;

  // Early return for current period
  if (config.operator_name === 'relative_date_current') {
    return anchor_period_time_unit;
  }

  if (['relative_date_last', 'relative_date_next'].includes(config.operator_name)) {
    const prefix = config.operator_name === 'relative_date_last' ? '-' : '';
    query_string = `${prefix}${anchor_period_starting_amount}_${anchor_period_starting_time_unit}`;
  }

  // Early return if starting point is today
  if (config.anchor_period_starting_point === 'today') {
    return query_string;
  }

  if (anchor_period_starting_point === 'after') {
    anchor_string = `${anchor_period_starting_amount}_${anchor_period_starting_time_unit}`;
  }
  if (anchor_period_starting_point === 'before') {
    anchor_string = `-${anchor_period_starting_amount}_${anchor_period_starting_time_unit}`;
  }
  if (anchor_period_starting_point === 'custom_date') {
    anchor_string = dayjs(anchor_period_starting_custom_date).format('YYYY-MM-DD');
  }

  return [query_string, anchor_string];
}
