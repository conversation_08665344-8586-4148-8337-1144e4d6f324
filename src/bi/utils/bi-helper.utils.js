import { generateChartConfig } from '@sensehawk/chart-generator';
import { applyClientSideFormatting, applyEChartsUnifiedInterTheme, applyFusionUnifiedInterTheme } from '@sensehawk/chart-generator/formatters';
import * as echarts from 'echarts';
import { cloneDeep } from 'lodash-es';
import { BI_HEATMAP_PALETTES, SERIES_SUPPORTED_CHARTS } from '~/bi/constants/bi-constants';
import { import_fusioncharts } from '~/common/utils/package.utils.js';

export async function generateChart(id, data, config, chart_instance) {
  if (!config)
    return;
  const base_config = generateChartConfig(data, config);

  if (chart_instance) {
    chart_instance.dispose();
    chart_instance = null;
  }

  if (base_config.metadata?.library !== 'fusioncharts') {
    const themed_config = applyEChartsUnifiedInterTheme(base_config, config);
    const formatted_config = applyClientSideFormatting(themed_config, config);

    const chartElement = document.getElementById(id);
    chart_instance = echarts.init(chartElement);
    chart_instance.setOption(formatted_config);
  }

  if (base_config.metadata?.library === 'fusioncharts') {
    const themed_config = applyFusionUnifiedInterTheme(base_config, config);

    const { VueFusionChartsComponent, FusionCharts, Charts, FusionTheme, TimeSeries, Widgets, Maps, PowerCharts, ExcelExport } = await import_fusioncharts();
    VueFusionChartsComponent(FusionCharts, Charts, FusionTheme, TimeSeries, Widgets, Maps, PowerCharts, ExcelExport);

    chart_instance = new FusionCharts({
      type: themed_config.type,
      renderAt: id,
      width: '100%',
      height: '100%',
      dataFormat: 'json',
      dataSource: themed_config.dataSource,
    });
    chart_instance.render();
  }

  return chart_instance;
}

export function parseConfigToFormData(original_config) {
  const config = cloneDeep(original_config);
  const chart = {
    type: `${config.type}_chart`,
  };

  // Layout tab - reverse data config
  if (config.data) {
    chart.layout_category = config.data.category;
    chart.layout_values = SERIES_SUPPORTED_CHARTS.includes(chart.type) ? config.data.values : config.data.values[0];
    chart.stack_by = config.data.stackBy || 'none';
  }

  // Series config - reverse series
  if (config.series) {
    const layout_values = [];
    Object.entries(config.series).forEach(([key, series]) => {
      layout_values.push({
        value: key,
        label: series.name,
        type: series.type,
        y_axis: series.yAxisIndex === 1 ? 'secondary' : 'primary',
        color: series.color,
        stack: series.stack !== false,
        line_width: series.lineWidth?.toString() || '1',
        line_style: series.lineStyle || 'solid',
        line_shape: series.smooth ? 'curved' : 'straight',
        prefix: series.prefix || '',
        suffix: series.suffix || '',
      });
    });
    chart.layout_values = layout_values;
  }

  // Display tab - reverse layout, legend, dataValues
  if (config.layout) {
    chart.title = config.layout.title;
    chart.subtitle = config.layout.subtitle;
  }
  if (config.legend) {
    chart.legend = config.legend.show ? config.legend.position : 'hide';
  }
  if (config.dataValues) {
    chart.values = config.dataValues.show ? 'show' : 'hide';
    chart.compact = config.dataValues.compact;
    chart.precision = config.dataValues.precision;
  }

  // Axes tab - reverse axes config
  if (config.axes) {
    chart.category_axis_name = config.axes.categoryName;
    chart.value_axis_name = config.axes.valueName;
    chart.secondary_y_axis = config.axes.secondaryValueName;
    chart.custom_range_min = config.axes.valueMin;
    chart.custom_range_max = config.axes.valueMax;
    chart.secondary_value_min = config.axes.secondaryValueMin;
    chart.secondary_value_max = config.axes.secondaryValueMax;
    chart.category_tick_label = config.axes.categoryLabels;
    chart.value_tick_label = config.axes.valueLabels;
    chart.secondary_value_tick_label = config.axes.secondaryValueLabels;
    chart.primary_scale = config.axes.valueScale;
    chart.secondary_scale = config.axes.secondaryValueScale;
  }

  // Advanced tab - reverse interactions
  if (config.interactions?.dataZoom) {
    chart.timeseries = config.interactions.dataZoom.enabled;
  }

  // Reference lines
  if (config.reference_lines) {
    chart.reference_lines = config.reference_lines.map(line => ({
      value: line.value.toString(),
      label: line.label,
      color: line.color,
      series: line.series,
      line_style: line.lineStyle,
    }));
  }

  // Chart specific configs
  if (config.chartSpecific) {
    const specific = config.chartSpecific;

    if (specific.pareto) {
      chart.show_eighty_percent_line = specific.pareto.show80PercentLine;
      chart.eighty_percent_line_color = specific.pareto.eightyPercentLineColor;
      chart.eighty_percent_line_style = specific.pareto.eightyPercentLineStyle;
      chart.bar_color = specific.pareto.barColor;
      chart.cumulative_line_color = specific.pareto.lineColor;
    }

    if (specific.heatmap) {
      chart.color_scheme = Object.keys(BI_HEATMAP_PALETTES).find(key =>
        BI_HEATMAP_PALETTES[key]?.colors === specific.heatmap.colorScheme,
      );
      chart.color_type = specific.heatmap.colorType;
      if (specific.heatmap.colorPieces) {
        chart.color_ranges = specific.heatmap.colorPieces.map(piece => ({
          label: piece.label,
          min: piece.min.toString(),
          max: piece.max.toString(),
          color: piece.color,
        }));
      }
    }

    if (specific.waterfall) {
      chart.show_sum = specific.waterfall.showSum;
      chart.positive_color = specific.waterfall.positiveColor;
      chart.negative_color = specific.waterfall.negativeColor;
    }

    if (specific.pyramid) {
      chart.show_labels_at_center = specific.pyramid.labelsAtCenter;
      chart.show_percentages = specific.pyramid.showPercentages;
      chart.render_in_3d = specific.pyramid.is3D;
    }

    if (specific.funnel) {
      chart.show_labels_at_center = specific.funnel.labelsAtCenter;
      chart.show_percentages = specific.funnel.showPercentages;
      chart.render_in_3d = specific.funnel.is3D;
      chart.compare_with_previous = specific.funnel.percentOfPrevious;
      chart.maintain_slope = specific.funnel.maintainSlope;
    }

    if (specific.gauge || specific.progress) {
      const gaugeConfig = specific.gauge || specific.progress;
      if (gaugeConfig.markers) {
        chart.markers = gaugeConfig.markers.map(marker => ({
          label: marker.label,
          value: marker.value.toString(),
          color: marker.color,
        }));
      }
      if (gaugeConfig.colorRange) {
        chart.color_ranges = gaugeConfig.colorRange.map(range => ({
          min: range.min.toString(),
          max: range.max.toString(),
          color: range.color,
        }));
      }
    }
  }

  return { chart };
}
