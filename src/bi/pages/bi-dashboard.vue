<script setup>
import * as echarts from 'echarts';
import jsPDF from 'jspdf';
import { keyBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted } from 'vue';
import { useModal } from 'vue-final-modal';
import BiCreateDashboardModal from '~/bi/components/bi-create-dashboard-modal.vue';
import BiCreateWidget from '~/bi/components/bi-create-widget.vue';
import { DASHBOARDS } from '~/bi/constants/temporary-grid-data.js';
import { useBiStore } from '~/bi/store/bi.store';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import { sleep } from '~/common/utils/common.utils';
import { load_js_css_file } from '~/common/utils/load-script.util';

const $t = inject('$t');

const bi_store = useBiStore();
const { dashboards, selected_dashboard, selected_dashboard_details, all_widget_details } = storeToRefs(bi_store);

const state = reactive({
  is_loading: false,
  is_editing_grid: false,
  dashboard_layout: [],
  show_sidebar: false,
  is_exporting: false,
  is_saving: false,
});

const create_widget = useModal({
  component: BiCreateWidget,
  attrs: {
    onClose() {
      create_widget.close();
    },
  },
});

const create_dashboard = useModal({
  component: BiCreateDashboardModal,
  attrs: {
    onClose() {
      create_dashboard.close();
    },
    async onSave(name) {
      // TODO: API call here for creating the dashboard
      await sleep(1000);
      const new_dashboard_uid = crypto.randomUUID();
      dashboards.value = {
        ...dashboards.value,
        [new_dashboard_uid]: {
          uid: new_dashboard_uid,
          name,
          widgets: [],
        },
      };
      selected_dashboard.value = new_dashboard_uid;
      create_dashboard.close();
    },
  },
});

const delete_popup = useModal({
  component: HawkDeletePopup,
  attrs: {
    onClose() {
      delete_popup.close();
    },
  },
});

const dashboard_action_items = computed(() => {
  return [
    { label: $t('Edit'), on_click: () => (state.is_editing_grid = true) },
    { label: $t('Export PDF'), on_click: () => setExportingState(true) },
    { label: $t('Archive'), on_click: () => {} },
    { label: $t('Delete'), on_click: () => deleteDashboard() },
  ];
});

const bi_grid = ref(null);

function createDashboard() {
  create_dashboard.open();
}

function createWidget() {
  create_widget.patchOptions({
    attrs: {
      mode: 'create',
      view: 'data-builder',
      widgetData: [],
      config: {},
      onClose() {
        create_widget.close();
      },
    },
  });
  create_widget.open();
}

async function onSave() {
  state.is_saving = true;
  state.dashboard_layout = bi_grid.value.getCurrentLayout();
  // TODO: API call here for saving the dashboard
  await sleep(1000);
  await nextTick();
  state.is_editing_grid = false;
  state.is_saving = false;
}

function setExportingState(value) {
  state.is_exporting = value;
}

async function exportDashboard() {
  await load_js_css_file(
    'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js',
    'html2canvas-js',
    'js',
  );

  const widgets = document.querySelectorAll('.vgl-item:not(.vgl-item--placeholder)');

  const sorted_widgets = Array.from(widgets).sort((a, b) => {
    const a_layout = state.dashboard_layout.find(item => item.widget_id === a.getAttribute('widget_id'));
    const b_layout = state.dashboard_layout.find(item => item.widget_id === b.getAttribute('widget_id'));

    if (!a_layout || !b_layout)
      return 0;

    if (a_layout.y !== b_layout.y) {
      return a_layout.y - b_layout.y;
    }
    return a_layout.x - b_layout.x;
  });

  const bi_grid_container = bi_grid.value.$el || document.querySelector('.vue-grid-layout');
  const CONTAINER_WIDTH = bi_grid_container ? bi_grid_container.offsetWidth : 1200; // fallback to 1200px
  const A4_WIDTH = CONTAINER_WIDTH - 300;
  const A4_HEIGHT = 1123;
  const MARGIN = 20;
  const GRID_HEIGHT_SCALE = 30; // 1 grid unit = 30px for height
  const WIDGET_SPACING = 8;
  const WIDGET_BORDER_WIDTH = 1;
  const WIDGET_BORDER_COLOR = '#e0e0e0';
  const GRID_WIDTH_SCALE = (A4_WIDTH - 2 * MARGIN - WIDGET_SPACING) / 12;

  let last_page_broke_at = 0;
  let current_page_height = A4_HEIGHT;

  let current_page = 0;
  const pages = [
    {
      height: A4_HEIGHT,
      width: A4_WIDTH,
      widgets: [],
    },
  ];

  function hasStartedBeforeAndEndedAfter(widget_layout) {
    const current_y = widget_layout.y;
    const current_h = widget_layout.h;
    return state.dashboard_layout
      .some((item) => {
        return ((item.y < current_y && item.y + item.h > current_y) || (item.y >= current_y && item.y + item.h < current_y + current_h));
      });
  }

  for (let i = 0; i < sorted_widgets.length; i++) {
    const widget_layout = state.dashboard_layout.find(item => item.widget_id === sorted_widgets[i].getAttribute('widget_id'));
    const widget_height = widget_layout.h * GRID_HEIGHT_SCALE;
    const widget_width = widget_layout.w * GRID_WIDTH_SCALE;

    const x_position = MARGIN + (widget_layout.x * GRID_WIDTH_SCALE) + (widget_layout.x > 0 ? WIDGET_SPACING : 0);
    let y_position = MARGIN + ((widget_layout.y - last_page_broke_at) * GRID_HEIGHT_SCALE) + (widget_layout.y > last_page_broke_at ? WIDGET_SPACING : 0) + 15;

    const adjusted_width = widget_width - (widget_layout.x > 0 ? WIDGET_SPACING / 2 : 0) - (widget_layout.x + widget_layout.w < 12 ? WIDGET_SPACING / 2 : 0);
    const adjusted_height = widget_height - WIDGET_SPACING / 2;

    if (!hasStartedBeforeAndEndedAfter(widget_layout) && current_page_height > A4_HEIGHT && y_position + adjusted_height > current_page_height) {
      last_page_broke_at = widget_layout.y;
      current_page_height = A4_HEIGHT;
      pages.push({
        height: A4_HEIGHT,
        width: A4_WIDTH,
        widgets: [],
      });
      current_page++;
      y_position = MARGIN + ((widget_layout.y - last_page_broke_at) * GRID_HEIGHT_SCALE) + (widget_layout.y > last_page_broke_at ? WIDGET_SPACING : 0);
    }
    else if (y_position + adjusted_height > current_page_height) {
      pages[current_page].height = y_position + adjusted_height + MARGIN;
      current_page_height = y_position + adjusted_height + MARGIN;
    }

    let img;

    // Only echarts instances have <canvas> element and not fusion charts
    const echarts_canvas = sorted_widgets[i].querySelector('canvas');
    const echarts_instance = echarts_canvas ? echarts.getInstanceByDom(echarts_canvas.parentElement.parentElement) : null;

    if (echarts_instance) {
      img = echarts_instance.getDataURL({
        type: 'jpeg',
        pixelRatio: 2,
        backgroundColor: '#ffffff',
      });
    }
    else {
      const canvas = await html2canvas(sorted_widgets[i].children[0].children[1], {
        scale: 2,
        backgroundColor: '#ffffff',
        useCORS: true,
        allowTaint: true,
        imageTimeout: 0,
        onclone: (document) => {
          const allElements = document.querySelectorAll('*');
          Array.from(allElements).forEach((element) => {
            element.style.overflow = 'visible';
            element.style.textOverflow = 'clip';
          });

          // const th_elements = document.querySelectorAll('th');
          // Array.from(th_elements).forEach((element) => {
          //   element.style.overflow = 'visible';
          //   element.style.textOverflow = 'clip';
          //   // element.style.marginTop = '-3px';
          // });

          // const td_elements = document.querySelectorAll('td');
          // Array.from(td_elements).forEach((element) => {
          //   element.style.overflow = 'visible';
          //   element.style.textOverflow = 'clip';
          //   // element.style.marginTop = '-3px';
          // });
        },
      });

      img = canvas.toDataURL('image/png', 1.0);
    }

    pages[current_page].widgets.push({
      img,
      x: x_position,
      y: y_position,
      width: adjusted_width,
      height: adjusted_height,
      title: all_widget_details.value[widget_layout.widget_id].name,
    });
  }

  // eslint-disable-next-line new-cap
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'px',
    format: [A4_WIDTH, pages[0].height],
  });

  pages.forEach((page, index) => {
    if (index > 0)
      doc.addPage([A4_WIDTH, page.height]);
    page.widgets.forEach((widget) => {
      doc.setDrawColor(WIDGET_BORDER_COLOR);
      doc.setLineWidth(WIDGET_BORDER_WIDTH);
      doc.rect(
        widget.x - WIDGET_BORDER_WIDTH,
        widget.y - WIDGET_BORDER_WIDTH,
        widget.width + (2 * WIDGET_BORDER_WIDTH),
        widget.height + (2 * WIDGET_BORDER_WIDTH),
      );

      if (widget.title) {
        doc.setFontSize(14);
        doc.setTextColor('#101828');
        doc.text(widget.title, widget.x + 8, widget.y + 15);
      }

      doc.addImage(
        widget.img,
        'PNG',
        // The two separate additions/subtractions are to 1. Make space for the title 2. Give padding inside the box
        widget.x + 10,
        widget.y + 20 + 10,
        widget.width - 20,
        widget.height - 20 - 20,
      );
    });
  });

  doc.save(`${selected_dashboard_details.value.name}.pdf`);
}

function deleteDashboard() {
  delete_popup.patchOptions({
    attrs: {
      header: `Delete dashboard: "${selected_dashboard_details.value.name}"`,
      content: 'Are you sure you want to delete this dashboard?',
      confirm: async () => {
        // TODO: API call here for deleting the dashboard
        await sleep(1000);
        delete dashboards.value[selected_dashboard.value];
        selected_dashboard.value = Object.keys(dashboards.value)[0];
        delete_popup.close();
      },
    },
  });
  delete_popup.open();
}

watch(() => selected_dashboard_details.value, (new_val) => {
  if (new_val.uid) {
    state.dashboard_layout = dashboards.value[new_val.uid].widgets;
  }
}, { deep: true });

onMounted(async () => {
  state.is_loading = true;
  // TODO: API call here for getting the dashboards
  await sleep(1000);
  dashboards.value = keyBy(DASHBOARDS, 'uid');
  selected_dashboard.value = DASHBOARDS[0].uid;
  state.dashboard_layout = DASHBOARDS[0].widgets;
  state.is_loading = false;
});
</script>

<template>
  <div>
    <HawkLoader v-if="state.is_loading" />
    <div v-else class="flex w-full items-start">
      <DashboardSidebar :show_sidebar="state.show_sidebar" class="top-[65px]" />
      <div class="w-full">
        <div class="sticky top-[65px] z-10 bg-white">
          <div class="px-4 pt-3 flex items-center gap-3">
            <HawkButton
              icon
              type="text"
              class="-mt-3 -ml-2 mr-2"
              @click="state.show_sidebar = !state.show_sidebar"
            >
              <IconHawkMenuThree />
            </HawkButton>
            <HawkTabs
              :tabs="Object.values(dashboards).map(item => ({ uid: item.uid, label: item.name }))"
              :active_item="selected_dashboard"
              container_class="border-transparent"
              @tab-click="selected_dashboard = $event.uid"
            />
            <HawkButton
              icon
              type="text"
              class="-mt-3"
              @click="createDashboard"
            >
              <IconHawkPlus />
            </HawkButton>
          </div>
          <hr>
          <div class="px-4 py-2 bg-gray-50 flex justify-between items-center">
            <div class="flex items-center">
              Filters (Coming soon™)
              <HawkButton v-if="state.is_editing_grid" icon type="text" class="hover:!bg-gray-200">
                <IconHawkSettingsOne />
              </HawkButton>
            </div>
            <div v-if="state.is_editing_grid" class="flex items-center gap-1">
              <HawkButton type="text" @click="createWidget">
                <IconHawkPlus class="text-primary-700" />
                <span class="whitespace-nowrap text-primary-700">
                  Add widget
                </span>
              </HawkButton>
              <HawkButton :loading="state.is_saving" @click="onSave">
                <IconHawkRocketTwo />
                Publish
              </HawkButton>
            </div>
            <div v-else class="flex items-center gap-3">
              <HawkMenu :items="dashboard_action_items">
                <template #trigger>
                  <HawkButton icon type="text" class="hover:!bg-gray-200">
                    <IconHawkSettingsOne />
                  </HawkButton>
                </template>
              </HawkMenu>
            </div>
          </div>
          <hr>
        </div>
        <BiGrid
          ref="bi_grid"
          :layout="state.dashboard_layout"
          :is-editing="state.is_editing_grid"
        />
      </div>
      <HawkExportToast
        v-if="state.is_exporting"
        :submit="exportDashboard"
        :progress_text="$t('Exporting PDF')"
        :completed_text="$t('Exported PDF')"
        :show_cancel_button="false"
        @close="setExportingState(false)"
      />
    </div>
  </div>
</template>
