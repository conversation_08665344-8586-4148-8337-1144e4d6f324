import { supportedAggregations } from '@sensehawk/query-generator/aggregations';
import IconHawkArrowDown from '~icons/hawk/arrow-down';
import IconHawkArrowUp from '~icons/hawk/arrow-up';
import IconHawkBinary from '~icons/hawk/binary';
import IconHawkBoolean from '~icons/hawk/boolean';
import IconHawkBracketsEllipses from '~icons/hawk/brackets-ellipses';
import calendar from '~icons/hawk/calendar';
import IconHawkClock from '~icons/hawk/clock';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkInnerJoin from '~icons/hawk/inner-join';
import IconHawkOuterJoin from '~icons/hawk/outer-join';
import IconHawkRightJoin from '~icons/hawk/right-join';
import IconHawkTypeOne from '~icons/hawk/type-one';
import IconHawkLeftJoin from '~icons/hawk/vector-join';

function getIconsForType(type) {
  const icons_type_map = {
    'string': IconHawkTypeOne,
    'text': IconHawkTypeOne,
    'varchar': IconHawkTypeOne,
    'char': IconHawkTypeOne,
    'numeric': IconHawkHashTwo,
    'integer': IconHawkHashTwo,
    'bigint': IconHawkHashTwo,
    'smallint': IconHawkHashTwo,
    'decimal': IconHawkHashTwo,
    'float': IconHawkHashTwo,
    'double': IconHawkHashTwo,
    'real': IconHawkHashTwo,
    'number': IconHawkHashTwo,
    'double precision': IconHawkHashTwo,
    'date': calendar,
    'datetime': calendar,
    'timestamp': calendar,
    'timestamptz': IconHawkClock,
    'boolean': IconHawkBoolean,
    'bool': IconHawkBoolean,
    'interval': IconHawkClock,
    'time': IconHawkClock,
    'binary': IconHawkBinary,
    'bytea': IconHawkBinary,
    'json': IconHawkBracketsEllipses,
    'jsonb': IconHawkBracketsEllipses,
    'uuid': IconHawkHashTwo,
    'id': IconHawkHashTwo,
    'function': IconHawkFunction,
    'formula': IconHawkFormula,
    'asc': IconHawkArrowUp,
    'desc': IconHawkArrowDown,
    'joins': {
      inner: IconHawkInnerJoin,
      outer: IconHawkOuterJoin,
      right: IconHawkRightJoin,
      left: IconHawkLeftJoin,
    },
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

const aggregations = supportedAggregations();
const supported_types = Array.from(new Set(aggregations.map(agg => agg.supportedTypes).flat()));

const operators_for_type = Array.from(supported_types).reduce((obj, type) => {
  obj[type] = aggregations.map(agg => ({
    label: agg.label,
    name: agg.name,
    output_type: agg.outputTypes[type],
  })).filter(agg => agg.output_type);
  return obj;
}, {});

function getOperatorsForType(type) {
  return operators_for_type[type] || [];
}

function buildHierarchy(cols) {
  const root = [];

  const findHierarchyNode = (arr, label) => arr.find(n => n.label === label && n.is_hierarchy);

  cols.forEach((col) => {
    if (Array.isArray(col.path) && col.path.length > 0) {
      let currentLevel = root;
      // Walk/create nodes for each part in the path.
      col.path.forEach((part, idx) => {
        const isLast = idx === col.path.length - 1;
        // If it's the last part, ensure there's a node and push the column into its children.
        if (isLast) {
          let node = findHierarchyNode(currentLevel, part);
          if (!node) {
            node = { label: part, is_hierarchy: true, children: [] };
            currentLevel.push(node);
          }
          node.children.push(col);
        }
        else {
          // intermediate node: ensure it exists and descend into its children
          let node = findHierarchyNode(currentLevel, part);
          if (!node) {
            node = { label: part, is_hierarchy: true, children: [] };
            currentLevel.push(node);
          }
          currentLevel = node.children;
        }
      });
    }
    else {
      // No path => top-level column
      root.push(col);
    }
  });

  return root;
}

export function useBIQueryBuilder() {
  const getColumnText = column_name => `${column_name}`;
  const getAggregationText = agg => `${agg} of `;
  const getBinText = bin => ` (${bin})`;
  const getTableText = table_name => `${table_name} ⇒ `;
  const constructFieldName = (field, selected_table_name) => field.table_name ? (`${field.table_name}→${field.label}`) : (`${selected_table_name}→${field.label}`);
  const constructAlias = (fn, param) => param ? fn(param) : ''; // this will construct the alias for the column ${table_name} -- ${agg} ${column_name}
  const constructColumnAlias = ({ table_name, column_name, agg, bin }) => constructAlias(getTableText, table_name) + constructAlias(getAggregationText, agg) + constructAlias(getColumnText, column_name) + constructAlias(getBinText, bin);

  function createDefaultSelection() {
    return {
      columns: [],
      orderBy: [],
      limit: null,
      filters: [],
    };
  }

  function getStageConfig(stages) {
    if (stages[0]?.value?.columns?.length === 0)
      return;
    const getBin = field => ({ [field.type === 'date' ? 'dateBin' : 'binWidth']: field.bin });
    const getFunction = field => ({ agg: field.agg, ...(field.is_bin ? getBin(field) : {}) });
    const getField = (field, selected_table_name = 'results') => ({ field: constructFieldName(field, selected_table_name), ...getFunction(field), alias: field.alias });
    const getExpression = field => ({ expr: field.expression, type: field.aggregation_type === 'aggregations' ? 'aggregation' : 'column', alias: field.alias });
    const getJoin = (stage) => {
      if (stage.tables.length > 1) {
        const joins = stage.tables.filter((t, index) => index > 0).map((table) => {
          return {
            table: { name: table.label, alias: table.alias },
            type: table.type,
            on: { left: table.on.left, right: `${table.on.right}` },
          };
        });
        return joins;
      }
      else {
        return undefined;
      }
    };

    const getTableConfig = stage => ({
      table: stage.selected_table?.label,
      orderBy: stage.value.orderBy.map(column => ({ column: column.alias, direction: column.direction })),
      limit: stage.value.limit ? stage.value.limit : undefined,
      filters: stage.value.filters.map(filter => ({
        ...getField(filter.column_config, stage.selected_table?.label ? `${stage.selected_table.label}→` : ''),
        op: filter.filter_config.operator_name || filter.filter_config.operator.name, // operator_name is used for contains/icontains/relative_date etc cases
        value: filter.filter_config.operator_value,
      })),
      columns: stage.value.columns.map(field => field.expression ? getExpression(field) : getField(field, stage.selected_table?.label)),
      joins: getJoin(stage),
    });
    const config = stages.filter(stage => stage.value?.columns?.length > 0).map(stage => getTableConfig(stage));
    return config;
  }

  return {
    constructColumnAlias,
    constructFieldName,
    getIconsForType,
    getOperatorsForType,
    createDefaultSelection,
    buildHierarchy,
    getStageConfig,
  };
}
