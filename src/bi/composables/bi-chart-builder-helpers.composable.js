import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { CHART_TO_VALUE_TYPE_MAP } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

export function useBiChartBuilderHelpers() {
  const bi_store = useBiStore();
  const { widget_data, chart_builder_data_types, widget_builder_config } = storeToRefs(bi_store);

  const all_columns = computed(() => {
    return Object.keys(widget_data.value[0]);
  });

  const markers_value_options = computed(() => {
    return all_columns.value.filter((column) => {
      if (widget_builder_config.value.chart.layout_values === column)
        return false;
      if (CHART_TO_VALUE_TYPE_MAP[widget_builder_config.value.chart.type].includes(chart_builder_data_types.value[column]))
        return true;
      return false;
    });
  });

  return {
    all_columns,
    markers_value_options,
  };
}
