<script setup>
import BIFilter from '~/bi/components/filters/bi-filter-list/bi-filter/bi-filter.vue';

defineProps({
  tables: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['apply']);

const is_expression_dropdown_open = ref(false);

function onApply($event, close = () => {}) {
  emit('apply', $event);
  close?.();
}

onMounted(() => {
});
</script>

<template>
  <div>
    <HawkMenu
      additional_trigger_classes="!ring-0 !focus:ring-0"
      additional_dropdown_classes="!shadow-none !border-none"
      :items="[]"
      position="fixed"
      @close="onClose"
    >
      <!-- @open="onOpen"
      @close="onClose" -->
      <template #trigger>
        <slot name="trigger_label" />
      </template>
      <template #content="{ close: closeOperatorMenu }">
        <div class="bg-transparent w-[356px]">
          <bi-query-builder-columns-dropdown
            :tables="tables"
            :stage-index="stageIndex"
            :is-dropdown="false"
            :is-filter="true"
            slot-container-classes="!min-h-0 w-[356px]"
            @selected="onColumnsSelected"
            @expression="is_expression_dropdown_open = true; is_dropdown_open = false;"
          >
            <template #default="{ column, table }">
              <!-- <BiFilterList v-if="!selected_column" :columns="columns" container-classes="max-h-60 scrollbar" @filter-selected="onFilterSelected" /> -->
              <BIFilter v-if="column" :selected-column-config="{ ...column, table_name: table.label }" @cancel="closeOperatorMenu" @apply="($event) => onApply($event, closeOperatorMenu)" />
            </template>
          </bi-query-builder-columns-dropdown>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
