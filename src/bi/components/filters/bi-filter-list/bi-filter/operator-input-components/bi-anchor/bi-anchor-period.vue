<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
  operatorConfig: {
    type: Object,
    default: () => ({}),
  },
  formData: {
    type: Object,
    default: null,
  },
});
// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const anchor_period_options = computed(() => {
  const obj = props.operatorConfig?.options?.direction?.valueMapping || {};
  const result = Object.entries(obj).map(([key, value]) => ({
    label: key,
    value,
  }));
  return result;
});

const default_operator_name = computed(() => {
  return props.filterConfig?.operator_name || props.operatorConfig?.options?.direction?.valueMapping?.[props.operatorConfig.options.direction.default];
});
// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
console.log('Anchor Period', props);
</script>

<template>
  <div class="col-span-12">
    <RadiogroupElement
      name="operator_name"
      :items="anchor_period_options"
      :default="default_operator_name"
      class="capitalize"
      :add-classes="{
        RadiogroupElement: {
          wrapper: ['!flex-row', 'gap-6'],
        },
      }"
    />
    <div class="flex gap-2 py-2">
      <TextElement
        name="anchor_period_time_amount"
        class="w-1/3"
        :default="props.filterConfig?.anchor_period_time_amount"
        input-type="number"
        autocomplete="off"
        rules="required"
        :conditions="[['operator_name', '!=', 'relative_date_current']]"
        @keydown.space.stop
      />
      <SelectElement
        name="anchor_period_time_unit"
        class="capitalize w-full"
        :default="props.filterConfig?.anchor_period_time_unit"
        :native="false"
        :items="props.formData?.operator_name === 'relative_date_current' ? ['week', 'month', 'quarter', 'year'] : ['weeks', 'months', 'quarters', 'years']"
      />
    </div>
    <div v-if="props.formData?.operator_name !== 'relative_date_current'" class="flex gap-2 py-2 items-center">
      <span class="text-xs text-gray-500 text-nowrap">Starting from</span>
      <SelectElement
        name="anchor_period_starting_point"
        class="capitalize w-2/3"
        :native="false"
        :default="props.filterConfig?.anchor_period_starting_point || 'today'"
        :can-clear="false"
        :items="[
          { label: 'Today', value: 'today' },
          { label: 'Before', value: 'before' },
          { label: 'After', value: 'after' },
          { label: 'Custom Date', value: 'custom_date' },
        ]"
      />
      <TextElement
        name="anchor_period_starting_amount"
        class="w-2/3"
        :default="props.filterConfig?.anchor_period_starting_amount"
        input-type="number"
        autocomplete="off"
        rules="required"
        :conditions="[['anchor_period_starting_point', '!=', 'today'], ['anchor_period_starting_point', '!=', 'custom_date']]"
        @keydown.space.stop
      />
      <SelectElement
        name="anchor_period_starting_time_unit"
        class="capitalize w-2/3"
        :default="props.filterConfig?.anchor_period_starting_time_unit"
        :can-clear="false"
        :native="false"
        :conditions="[['anchor_period_starting_point', '!=', 'today'], ['anchor_period_starting_point', '!=', 'custom_date']]"
        :items="['days', 'weeks', 'months', 'quarters', 'years']"
      />
      <DateTimeElement
        :options="{
          teleport: false,
          ...componentConfig.options,
          format: 'dd-MM-yyyy',
        }"
        :conditions="[['anchor_period_starting_point', '==', 'custom_date']]"
        :default="filterConfig?.anchor_period_starting_custom_date || null"
        name="anchor_period_starting_custom_date"
      />
    </div>
  </div>
</template>
