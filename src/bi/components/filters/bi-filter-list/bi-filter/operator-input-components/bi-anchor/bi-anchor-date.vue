<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
  operatorConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const anchor_date_options = computed(() => {
  const obj = props.operatorConfig?.options?.unit?.valueMapping || {};
  const result = Object.entries(obj).map(([key, value]) => ({
    label: value.split('_').join(' '),
    value,
  }));
  return result;
});

const default_operator_name = computed(() => {
  return props.filterConfig?.operator_name || props.operatorConfig?.options?.unit?.valueMapping?.[props.operatorConfig.options.unit.default];
});
// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
console.log('Anchor date', props);
</script>

<template>
  <div class="col-span-12">
    <SelectElement
      name="operator_name"
      class="capitalize w-full"
      :native="false"
      :default="default_operator_name"
      :items="anchor_date_options"
    />
    <div class="flex gap-2 py-4 items-center">
      <span class="text-xs text-gray-500 text-nowrap">Starting from</span>
      <SelectElement
        name="anchor_date_starting_point"
        class="capitalize w-2/3"
        :native="false"
        :default="props.filterConfig?.anchor_date_starting_point || 'today'"
        :can-clear="false"
        :can-deselect="false"
        :items="[
          { label: 'Today', value: 'today' },
          { label: 'Before', value: 'before' },
          { label: 'After', value: 'after' },
          { label: 'Custom Date', value: 'custom_date' },
        ]"
      />
      <TextElement
        name="anchor_date_starting_amount"
        class="w-2/3"
        input-type="number"
        autocomplete="off"
        :default="props.filterConfig?.anchor_date_starting_amount"
        rules="required"
        :conditions="[['anchor_date_starting_point', '!=', 'today'], ['anchor_date_starting_point', '!=', 'custom_date']]"
        @keydown.space.stop
      />
      <SelectElement
        name="anchor_date_starting_time_unit"
        class="capitalize w-2/3"
        :default="props.filterConfig?.anchor_date_starting_time_unit"
        :can-clear="false"
        :native="false"
        :conditions="[['anchor_date_starting_point', '!=', 'today'], ['anchor_date_starting_point', '!=', 'custom_date']]"
        :items="['days', 'weeks', 'months', 'quarters', 'years']"
      />
      <DateTimeElement
        :options="{
          teleport: false,
          ...componentConfig.options,
          format: 'dd-MM-yyyy',
        }"
        :conditions="[['anchor_date_starting_point', '==', 'custom_date']]"
        :default="filterConfig?.anchor_date_starting_custom_date || null"
        name="anchor_date_starting_custom_date"
      />
    </div>
  </div>
</template>
