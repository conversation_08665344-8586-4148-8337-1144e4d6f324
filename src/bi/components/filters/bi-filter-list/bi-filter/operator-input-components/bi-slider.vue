<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //

// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
</script>

<template>
  <div>
    <SliderElement :merge="-1" :default="filterConfig?.operator_value || [10, 20]" name="operator_value" />
  </div>
</template>
