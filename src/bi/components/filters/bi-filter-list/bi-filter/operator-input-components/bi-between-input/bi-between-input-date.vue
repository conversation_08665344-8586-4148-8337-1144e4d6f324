<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
  operatorConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //

// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
// console.log('Between Input Date Operator Component Loaded', props);
</script>

<template>
  <div class="col-span-12 flex gap-2">
    <DateTimeElement
      :options="{
        teleport: false,
        ...componentConfig.options.min_options,
      }"
      name="operator_value_min"
      :default="filterConfig?.operator_value_min || null"
    />
    <DateTimeElement
      :options="{
        teleport: false,
        ...componentConfig.options.max_options,
      }"
      name="operator_value_max"
      :default="filterConfig?.operator_value_max || null"
    />
  </div>
</template>
