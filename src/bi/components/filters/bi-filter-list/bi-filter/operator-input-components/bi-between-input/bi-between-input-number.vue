<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //

// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
onMounted(() => {
  console.log('Mounted bi-between-input-number', props);
});
</script>

<template>
  <div class="flex">
    <TextElement
      autocomplete="off"
      name="operator_value_min"
      :default="filterConfig?.operator_value_min || columnConfig?.stats?.minValue || null"
      input-type="number"
      class="w-full px-2"
      :placeholder="$t('Min')"
    />
    <TextElement
      autocomplete="off"
      name="operator_value_max"
      :default="filterConfig?.operator_value_max || columnConfig?.stats?.maxValue || null"
      input-type="number"
      class="w-full px-2"
      :placeholder="$t('Max')"
    />
  </div>
</template>
