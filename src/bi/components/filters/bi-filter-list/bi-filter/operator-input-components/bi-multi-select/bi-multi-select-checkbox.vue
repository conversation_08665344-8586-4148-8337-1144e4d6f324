<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //
// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //
// const checkbox_options = ref(props.columnConfig.options || []);
// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const checkbox_options = computed(() => {
  const options = props.columnConfig.options || [];
  return options;
});
const show_select_all = computed(() => {
  return checkbox_options.value.length > 1;
});
// -------------------------------- Functions ------------------------------- //
function toggleSelectAll(new_val, _old_val, el$) {
  if (new_val) {
    el$.form$.elements$.operator_value.checkAll();
  }
  else {
    el$.form$.elements$.operator_value.uncheckAll();
  }
}
// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
// console.log('Checkbox Operator Component Loaded', props);
</script>

<template>
  <div>
    <div class="scrollbar max-h-64">
      <CheckboxElement v-if="show_select_all" name="select_all" :default="filterConfig?.select_all || false" @change="toggleSelectAll">
        {{ $t('Select all') }}
      </CheckboxElement>
      <CheckboxgroupElement
        rules="required" name="operator_value" :items="checkbox_options" :default="filterConfig?.operator_value || []"
      />
    </div>
  </div>
</template>
