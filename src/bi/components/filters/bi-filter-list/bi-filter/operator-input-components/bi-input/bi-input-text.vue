<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
  operatorConfig: {
    type: Object,
    default: () => ({}),
  },
});

// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const has_case_sensitive_option = computed(() => {
  return !!props.operatorConfig?.options?.caseSensitive;
});
const case_sensitive_default = computed(() => {
  const mapped_default_value = props.operatorConfig?.options?.caseSensitive?.valueMapping[props.operatorConfig?.options?.caseSensitive?.default];

  return props.filterConfig?.operator_name || mapped_default_value;
});
// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
// console.log('Single Input Operator Component Loaded', props);
</script>

<template>
  <div>
    <template v-if="operatorConfig?.supportsMultipleValues">
      <ListElement
        class="max-h-48 scrollbar pt-2"
        name="operator_value"
        :controls="{ add: true, remove: true, sort: false }"
        :min="1"
        :initial="1"
        :add-classes="{
          ListElement: {
            add: ['hidden'],
            listItem: ['!flex'],
            remove: ['!ml-0 !flex-none'],
          },
        }"
        :default="filterConfig?.operator_value || [null]"
        :presets="['repeatable_list']"
        @keydown.space.stop
        @keydown.enter.stop
      >
        <template #default="{ index }">
          <TextElement
            class="!grow"
            :name="index"
            :placeholder=" $t('Enter value')"
            v-bind="componentConfig"
            input-type="text"
            autocomplete="off"
            rules="required"
          />
        </template>
      </ListElement>
    </template>
    <template v-else>
      <TextElement
        name="operator_value"
        :placeholder=" $t('Enter value')"
        class="pb-2 px-2"
        v-bind="componentConfig"
        input-type="text"
        autocomplete="off"
        rules="required"
        :default="filterConfig?.operator_value || null"
        @keydown.space.stop
      />
      <template v-if="has_case_sensitive_option">
        <CheckboxElement
          :default="case_sensitive_default"
          name="operator_name"
          :true-value="operatorConfig.options.caseSensitive.valueMapping.true"
          :false-value="operatorConfig.options.caseSensitive.valueMapping.false"
          class="px-2"
        >
          {{ operatorConfig.options.caseSensitive.label }}
        </CheckboxElement>
      </template>
    </template>
  </div>
</template>

<style scoped lang="scss">

</style>
