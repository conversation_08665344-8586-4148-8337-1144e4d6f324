<script setup>
import { storeToRefs } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';
import { parseConfigToFormData } from '~/bi/utils/bi-helper.utils.js';
import { sleep } from '~/common/utils/common.utils';

const props = defineProps({
  mode: {
    type: String,
    default: 'create',
    validator: value => ['create', 'edit'].includes(value),
  },
  view: {
    type: String,
    default: 'data-builder',
    validator: value => ['data-builder', 'chart-builder'].includes(value),
  },
  widgetName: {
    type: String,
    default: '',
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  widgetData: {
    type: Array,
    default: () => [],
  },
  widgetId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close']);

const bi_store = useBiStore();
const { dashboards, selected_dashboard, widget_builder_config, all_tables, widget_data, all_widget_details, table_metadata } = storeToRefs(bi_store);

const { createDefaultSelection } = useBIQueryBuilder();

const state = reactive({
  view: 'data-builder',
  is_table_selected: false,
  is_loading: false,
  widget_name: 'Untitled widget',
  is_renaming: false,
});

async function publishWidget(chart_config) {
  logger.log('PUBLISH WIDGET', widget_data.value, chart_config, widget_builder_config.value);
  if (props.mode === 'create') {
    const x = 0;
    const current_widgets = dashboards.value[selected_dashboard.value].widgets;
    const y = current_widgets.length > 0
      ? Math.max(...current_widgets.map(w => w.y + w.h))
      : 0;
    const unique_i = String(Math.max(...current_widgets.map(w => Number.parseInt(w.i) || 0)) + 1);
    const widget_id = crypto.randomUUID();
    all_widget_details.value[widget_id] = {
      name: state.widget_name,
      config: {
        query: widget_builder_config.value.query,
        chart: chart_config,
      },
      data: widget_data.value,
      table_metadata: table_metadata.value,
    };
    dashboards.value[selected_dashboard.value].widgets.push({
      x,
      y,
      w: 10,
      h: 10,
      i: unique_i,
      widget_id,
    });
    emit('close');
    await sleep(500);
    const el = document.querySelectorAll(`[widget_id='${widget_id}']`)?.[0];
    el?.scrollIntoView?.({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    });
  }
  else {
    all_widget_details.value[props.widgetId] = {
      name: state.widget_name,
      config: {
        query: widget_builder_config.value.query,
        chart: chart_config,
      },
      data: widget_data.value,
    };
    emit('close');
  }
}

async function selectTable(table) {
  state.is_loading = true;
  await bi_store.selectTable(table);
  widget_builder_config.value.query.stages.push({
    selected_table: bi_store.widget_builder_config.query.selected_table,
    value: createDefaultSelection(),
    tables: [bi_store.widget_builder_config.query.selected_table],
  });
  state.is_table_selected = true;
  state.is_loading = false;
}

function handleNameChange(name) {
  state.widget_name = name;
  state.is_renaming = false;
}

function closeEditor() {
  if (state.widget_name)
    state.is_renaming = false;
}

onMounted(async () => {
  state.is_loading = true;
  bi_store.reset_widget_builder();
  try { // For testing, remove this code once released to production
    const key = 'debug_bi_connection_id';
    let db = null;
    if (typeof localStorage !== 'undefined') {
      db = localStorage.getItem(key);
      if (!db) {
        db = 'grs_corvera';
        localStorage.setItem(key, db);
      }
    }
    else {
      db = 'grs_corvera';
    }
    bi_store.widget_builder_config.query.selected_database = db;
  }
  catch {
    bi_store.widget_builder_config.query.selected_database = 'grs_corvera';
  }
  await bi_store.getTables();
  if (props.mode === 'edit') {
    widget_builder_config.value = props.config;
    state.is_table_selected = true;
    if (props.view === 'chart-builder') {
      state.widget_name = props.widgetName;
      widget_data.value = props.widgetData;
      widget_builder_config.value.chart = parseConfigToFormData(props.config.chart).chart;
      state.view = 'chart-builder';
    }
  }
  state.is_loading = false;
});
</script>

<template>
  <HawkModalContainer v-if="!state.is_table_selected" content_class="h-full w-full rounded-none">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          Select a table
        </template>
      </HawkModalHeader>
      <HawkModalContent class="!p-0 max-h-[calc(100vh-85px)] overflow-auto">
        <div v-if="state.is_loading" class="text-sm font-medium text-gray-700 mb-2">
          <HawkLoader />
        </div>
        <div v-else>
          <div v-for="table in all_tables" :key="table" class="grid grid-cols-12 p-4">
            <div class="col-span-2 text-lg font-medium">
              {{ table.label }}
            </div>
            <div class="col-span-6">
              {{ table.columns.map(col => col.label).join(', ') }}
            </div>
            <div class="col-span-4">
              <hawk-button @click="selectTable(table)">
                Choose
              </hawk-button>
            </div>
          </div>
        </div>
      </HawkModalContent>
    </div>
  </HawkModalContainer>
  <BiChartBuilderLayout v-else @close="emit('close')">
    <template #title>
      <div class="font-semibold text-lg h-9">
        <HawkEditableName
          v-if="state.is_renaming"
          class="max-w-[50vw]"
          :name="state.widget_name"
          placeholder="Enter name of the widget"
          :input_classes="{
            TextElement: {
              input: 'font-semibold !text-xl !p-0',
              inputContainer: 'border-0 border-b',
            },
            ElementLayout: {
              innerWrapper: 'border-b',
            },
          }"
          @close="closeEditor"
          @update="handleNameChange($event)"
        />
        <div v-else class="group flex items-center gap-3 text-xl text-gray-900 font-semibold max-w-[65vw] mt-[3px]" @click="state.is_renaming = true">
          <span>
            {{ state.widget_name }}
          </span>
          <IconHawkEditOne class="w-4 h-4 group-hover:visible invisible cursor-pointer" @click="state.is_renaming = true" />
        </div>
      </div>
    </template>
    <template #left-content>
      <BiQueryBuilder v-if="state.view === 'data-builder'" />
      <BiChartBuilder v-else-if="state.view === 'chart-builder'" @go-back="state.view = 'data-builder'" />
    </template>
    <template #right-content>
      <BiQueryBuilderDataPreview
        v-if="state.view === 'data-builder'"
        @continue="state.view = 'chart-builder'"
      />
      <BiWidgetPreview
        v-else-if="state.view === 'chart-builder'"
        @continue="publishWidget"
      />
    </template>
  </BiChartBuilderLayout>
</template>
