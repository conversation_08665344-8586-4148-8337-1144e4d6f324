<script setup>
import { watchDebounced } from '@vueuse/core';
import { pick } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount, onMounted } from 'vue';
import BiBottomDrawer from '~/bi/components/common/bi-bottom-drawer.vue';
import BiTablePreview from '~/bi/components/widgets/table-widgets/bi-table-preview.vue';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { AXES_NAMES_SUPPORTED_CHARTS, BI_HEATMAP_PALETTES, DUAL_Y_AXIS_SUPPORTED_CHARTS, SERIES_SUPPORTED_CHARTS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';
import { generateChart } from '~/bi/utils/bi-helper.utils.js';

const emit = defineEmits(['continue']);

const { markers_value_options } = useBiChartBuilderHelpers();

const bi_store = useBiStore();
const { widget_builder_config, widget_data, are_chart_builder_fields_filled, chart_builder_data_types, table_metadata } = storeToRefs(bi_store);

let chart_instance = null;
const show_table_widget = ref(false);

async function generateConfig() {
  const config = {
    type: widget_builder_config.value.chart.type.replace('_chart', ''),
  };

  // Layout tab
  const values = Array.isArray(widget_builder_config.value.chart.layout_values)
    ? widget_builder_config.value.chart.layout_values
    : [{ value: widget_builder_config.value.chart.layout_values }];
  if (!are_chart_builder_fields_filled.value) {
    chart_instance?.dispose?.();
    return;
  }
  await nextTick();
  config.data = {
    category: widget_builder_config.value.chart.layout_category,
    values: values.map(item => item.value),
    ...(
      (['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'heatmap_chart'].includes(widget_builder_config.value.chart.type))
        ? {
            stackBy: widget_builder_config.value.chart.stack_by === 'none' ? null : widget_builder_config.value.chart.stack_by,
          }
        : {}),
  };

  // Series config
  if (widget_builder_config.value.chart.layout_values && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(widget_builder_config.value.chart.type)) {
    config.series = {
      ...Object.values(widget_builder_config.value.chart.layout_values).reduce((acc, item) => {
        acc[item.value] = {
          name: item.legend || item.value,
          type: item.type,
          ...(widget_builder_config.value.chart.type !== 'horizontalBar_chart' ? { yAxisIndex: item.y_axis === 'primary' ? 0 : 1 } : {}),
          color: item.color,
          ...(!item.stack ? { stack: false } : {}),
          lineColor: item.color,
          lineWidth: Number.parseInt(item.line_width),
          lineStyle: item.line_style,
          smooth: item.line_shape === 'curved',
          prefix: item.prefix,
          suffix: item.suffix,
        };
        return acc;
      }, {}),
    };
  }

  //  Display tab
  config.layout = {
    title: widget_builder_config.value.chart.title,
    subtitle: widget_builder_config.value.chart.subtitle,
  };
  config.legend = {
    show: widget_builder_config.value.chart.legend !== 'hide',
    position: widget_builder_config.value.chart.legend,
  };
  config.dataValues = {
    show: widget_builder_config.value.chart.values === 'show',
    compact: widget_builder_config.value.chart.compact,
    precision: widget_builder_config.value.chart.precision,
  };

  // Axes tab
  config.axes = {
    ...AXES_NAMES_SUPPORTED_CHARTS.includes(widget_builder_config.value.chart.type)
      ? {
          categoryName: widget_builder_config.value.chart.category_axis_name,
          valueName: widget_builder_config.value.chart.value_axis_name,
          ...(widget_builder_config.value.chart.secondary_y_axis ? { secondaryValueName: widget_builder_config.value.chart.secondary_y_axis } : {}),
        }
      : {},
    ...(SERIES_SUPPORTED_CHARTS.includes(widget_builder_config.value.chart.type)
      ? {
          // Custom ranges
          ...(!Number.isNaN(Number.parseInt(widget_builder_config.value.chart.custom_range_min)) ? { valueMin: Number.parseInt(widget_builder_config.value.chart.custom_range_min) } : {}),
          ...(!Number.isNaN(Number.parseInt(widget_builder_config.value.chart.custom_range_max)) ? { valueMax: Number.parseInt(widget_builder_config.value.chart.custom_range_max) } : {}),
          ...(DUAL_Y_AXIS_SUPPORTED_CHARTS.includes(widget_builder_config.value.chart.type)
            ? {
                ...(!Number.isNaN(Number.parseInt(widget_builder_config.value.chart.secondary_value_min)) ? { secondaryValueMin: Number.parseInt(widget_builder_config.value.chart.secondary_value_min) } : {}),
                ...(!Number.isNaN(Number.parseInt(widget_builder_config.value.chart.secondary_value_max)) ? { secondaryValueMax: Number.parseInt(widget_builder_config.value.chart.secondary_value_max) } : {}),
              }
            : {}),
          // Tick labels (Orientation)
          categoryLabels: widget_builder_config.value.chart.category_tick_label,
          valueLabels: widget_builder_config.value.chart.value_tick_label,
          secondaryValueLabels: widget_builder_config.value.chart.secondary_value_tick_label,
          // Scales (Linear, Log)
          valueScale: widget_builder_config.value.chart.primary_scale,
          secondaryValueScale: widget_builder_config.value.chart.secondary_scale,
        }
      : {}),
  };

  // Advanced tab
  if (
    ['column_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(widget_builder_config.value.chart.type)
    && widget_builder_config.value.chart.layout_category
    && chart_builder_data_types.value[widget_builder_config.value.chart.layout_category] === 'date'
  ) {
    config.interactions = {
      dataZoom: {
        enabled: widget_builder_config.value.chart.timeseries,
        type: 'both',
      },
    };
  }

  if (widget_builder_config.value.chart.reference_lines) {
    config.reference_lines = widget_builder_config.value.chart.reference_lines.map(line => ({
      value: ['min', 'max', 'average', 'median', 'p25', 'p75', 'p90', 'p95'].includes(line.value) ? line.value : Number.parseInt(line.value),
      label: line.label,
      color: line.color,
      series: line.series,
      lineStyle: line.line_style,
    })).filter(line => line.value && line.series);
  }

  // Chart specific config
  if (widget_builder_config.value.chart.type === 'pareto_chart') {
    config.chartSpecific = {
      pareto: {
        show80PercentLine: widget_builder_config.value.chart.show_eighty_percent_line,
        eightyPercentLineColor: widget_builder_config.value.chart.eighty_percent_line_color,
        eightyPercentLineStyle: widget_builder_config.value.chart.eighty_percent_line_style,
        barColor: widget_builder_config.value.chart.bar_color,
        lineColor: widget_builder_config.value.chart.cumulative_line_color,
      },
    };
  }
  else if (widget_builder_config.value.chart.type === 'heatmap_chart') {
    config.chartSpecific = {
      heatmap: {
        colorScheme: BI_HEATMAP_PALETTES[widget_builder_config.value.chart.color_scheme]?.colors,
        colorType: widget_builder_config.value.chart.color_type,
        ...(widget_builder_config.value.chart.color_ranges?.length
          ? {
              colorPieces: widget_builder_config.value.chart.color_ranges.map(range => ({
                label: range.label,
                min: markers_value_options.value.includes(range.min) ? range.min : Number.parseInt(range.min),
                max: markers_value_options.value.includes(range.max) ? range.max : Number.parseInt(range.max),
                color: range.color,
              })),
            }
          : {}),
      },
    };
  }
  else if (widget_builder_config.value.chart.type === 'waterfall_chart') {
    config.chartSpecific = {
      waterfall: {
        showSum: widget_builder_config.value.chart.show_sum,
        positiveColor: widget_builder_config.value.chart.positive_color,
        negativeColor: widget_builder_config.value.chart.negative_color,
      },
    };
  }
  else if (widget_builder_config.value.chart.type === 'pyramid_chart') {
    config.chartSpecific = {
      pyramid: {
        labelsAtCenter: widget_builder_config.value.chart.show_labels_at_center,
        showPercentages: widget_builder_config.value.chart.show_percentages,
        is3D: widget_builder_config.value.chart.render_in_3d,
      },
    };
  }
  else if (widget_builder_config.value.chart.type === 'funnel_chart') {
    config.chartSpecific = {
      funnel: {
        labelsAtCenter: widget_builder_config.value.chart.show_labels_at_center,
        showPercentages: widget_builder_config.value.chart.show_percentages,
        is3D: widget_builder_config.value.chart.render_in_3d,
        percentOfPrevious: widget_builder_config.value.chart.compare_with_previous,
        maintainSlope: widget_builder_config.value.chart.maintain_slope,
      },
    };
  }
  else if (['gauge_chart', 'progress_chart'].includes(widget_builder_config.value.chart.type)) {
    config.chartSpecific = {
      [widget_builder_config.value.chart.type.replace('_chart', '')]: {
        ...(widget_builder_config.value.chart.markers
          ? {
              markers: widget_builder_config.value.chart.markers.map(marker => ({
                label: marker.label,
                value: markers_value_options.value.includes(marker.value) ? marker.value : Number.parseInt(marker.value),
                color: marker.color,
              })),
            }
          : {}),
        ...(widget_builder_config.value.chart?.color_ranges?.length
          ? {
              colorRange: widget_builder_config.value.chart.color_ranges.map(range => ({
                min: markers_value_options.value.includes(range.min) ? range.min : Number.parseInt(range.min),
                max: markers_value_options.value.includes(range.max) ? range.max : Number.parseInt(range.max),
                color: range.color,
              })),
            }
          : {}),
      },
    };
  }

  return config;
}

async function renderWidget() {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type)) {
    show_table_widget.value = true;
    // Probably some data processing
  }
  else {
    const config = await generateConfig();
    chart_instance = await generateChart('chart-container', widget_data.value, config, chart_instance);
  }
}

async function onPublishWidget() {
  let config = {};
  if (widget_builder_config.value.chart.type === 'table') {
    const {
      columns_map = {},
      show_row_headers = false,
      conditional_formatting = [],
    } = widget_builder_config.value.chart;
    config = {
      type: 'table',
      columns_map,
      show_row_headers,
      conditional_formatting,
    };
  }
  else if (widget_builder_config.value.chart.type === 'pivot_table') {
    const {
      pivot_rows = [],
      pivot_columns = [],
      pivot_values = [],
      show_row_totals = false,
      show_column_totals = false,
      show_grand_total = false,
      conditional_formatting = [],
    } = widget_builder_config.value.chart;
    config = {
      type: 'pivot_table',
      pivot_rows,
      pivot_columns,
      pivot_values,
      show_row_totals,
      show_column_totals,
      show_grand_total,
      conditional_formatting,
    };
  }
  else {
    config = await generateConfig();
  }
  emit('continue', config);
}

watchDebounced(
  () => widget_builder_config.value.chart,
  async () => {
    await nextTick();
    renderWidget();
  },
  { deep: true, immediate: true, debounce: 300 },
);

onBeforeUnmount(() => {
  if (chart_instance) {
    chart_instance.dispose();
    chart_instance = null;
  }
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      <div v-if="!are_chart_builder_fields_filled" class="h-full w-full flex flex-col justify-center items-center text-center">
        <IconIllustrationBiEmptyChartBuilder />
        <div class="text-sm font-semibold text-gray-900 mt-4 mb-1">
          No chart to show
        </div>
        <div class="text-sm font-normal text-gray-600">
          Configure the chart from left panel to start generating the chart
        </div>
      </div>
      <div v-else-if="['table', 'pivot_table'].includes(widget_builder_config.chart.type)" class="w-full h-full">
        <BiTablePreview
          v-if="show_table_widget"
          :key="`${widget_builder_config.chart.type}`"
          :is-builder="false"
          :is-editing="true"
          :config="bi_store.widget_builder_config"
          :data="widget_data"
          :table-metadata="table_metadata"
        />
      </div>
      <div v-else id="chart-container" class="h-full w-full" />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton @click="onPublishWidget">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
