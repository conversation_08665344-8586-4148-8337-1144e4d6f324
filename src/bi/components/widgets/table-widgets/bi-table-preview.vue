<script setup>
import { cloneDeep, get, orderBy } from 'lodash-es';
import { computed, nextTick, onMounted, reactive } from 'vue';
import BiHandsontable from '~/bi/components/widgets/table-widgets/bi-handsontable.vue';
import { useTableConditionalFormatter } from '~/bi/composables/bi-table-conditional-formatter.composable.js';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  id: {
    type: String,
    default: () => crypto.randomUUID(),
  },
  data: {
    type: Array,
    default: null,
  },
  tableMetadata: {
    type: Object,
    default: () => ({}),
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  isBuilder: {
    type: Boolean,
    default: true,
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  height: {
    type: Number,
    default: 0,
  },
});

const bi_store = useBiStore();
const { checkRuleMatch, getContrastTextColor, getLighterColor, getColorForValue } = useTableConditionalFormatter();

const state = reactive({
  loading: false,
  error: null,
  data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: false,
  column_config: {},

  table_instance: null,

  changes_detected: false,
  loading_changes: false,
  force_update: 1, // Will be updated in the future to update settings
});

const chart_config = computed(() => {
  const chart_details = props.config?.chart || {};
  const {
    type,

    columns_map = {},
    show_row_headers = false,

    pivot_rows = [],
    pivot_columns = [],
    pivot_values = [],
    show_row_totals = false,
    show_column_totals = false,
    show_grand_totals = false,

    conditional_formatting = [],
  } = chart_details;

  if (type === 'table') {
    return { type, columns_map, conditional_formatting, show_row_headers };
  }
  else if (type === 'pivot_table') {
    return { type, pivot_rows, pivot_columns, pivot_values, show_row_totals, show_grand_totals, show_column_totals, conditional_formatting };
  }
  return chart_details;
});
const enable_row_headers = computed(() => {
  if (chart_config.value?.type === 'table')
    return chart_config.value?.show_row_headers || false;
  return false;
});
const table_height = computed(() => {
  if (props.height)
    return props.height;
  // Header and Footer is about 85 each total 190
  // Additional table padding 40
  const available_height = window.innerHeight - 220;
  return available_height;
});
// -------------------------------- Methods --------------------------------- //
function getTableColumn(col) {
  const is_table = chart_config.value?.type === 'table';

  // Handle the case when `col` is a string
  if (typeof col === 'string') {
    const width = is_table ? chart_config.value?.columns_map?.[col]?.width ?? null : null;

    return {
      label: col,
      data: col,
      type: 'text',
      readOnly: true,
      field: col,
      ...(width && { width }),
    };
  }

  // When `col` is an object
  const key = col.key || col.label;
  const field = col.field || key;
  const width = is_table ? chart_config.value?.columns_map?.[key]?.width ?? null : null;

  return {
    label: col.label,
    data: key,
    type: 'text',
    readOnly: true,
    field,
    ...(width && { width }),
  };
}

async function setupPivotTable(reinitialize = true) {
  if (props.isEditing && reinitialize) {
    const current_columns = state.columns;

    // Previous config columns
    const prev_rows = chart_config.value.pivot_rows || [];
    const prev_cols = chart_config.value.pivot_columns || [];
    const prev_vals = chart_config.value.pivot_values || [];

    const alias_map = bi_store.alias_to_column_mapping();

    const previous_columns = [...prev_rows, ...prev_cols, ...prev_vals];

    const current_set = new Set(current_columns);
    const previous_set = new Set(previous_columns);

    const has_column_changes = (
      current_columns.length !== previous_columns.length
      || current_columns.some(col => !previous_set.has(col))
      || previous_columns.some(col => !current_set.has(col))
    );

    if (has_column_changes) {
      const updated_rows = [];
      const updated_cols = [];
      const updated_vals = [];

      // Re-add existing ones (that still exist)
      for (const col of prev_rows) {
        if (current_set.has(col))
          updated_rows.push(col);
      }
      for (const col of prev_cols) {
        if (current_set.has(col))
          updated_cols.push(col);
      }
      for (const col of prev_vals) {
        if (current_set.has(col))
          updated_vals.push(col);
      }

      // Identify new columns
      const new_columns = current_columns.filter(col => !previous_set.has(col));

      // Classify and assign new columns
      const new_value_keys = [];
      const new_non_value_keys = [];

      for (const col of new_columns) {
        const details = alias_map[col];
        if (details?.is_aggregation || ['integer', 'numeric'].includes(details?.type)) {
          new_value_keys.push(col);
        }
        else {
          new_non_value_keys.push(col);
        }
      }

      // Assign new value keys
      updated_vals.push(...new_value_keys);

      // Assign new non-value keys by balancing between rows and columns
      for (const col of new_non_value_keys) {
        if (updated_rows.length <= updated_cols.length) {
          updated_rows.push(col);
        }
        else {
          updated_cols.push(col);
        }
      }

      bi_store.widget_builder_config.chart.pivot_rows = updated_rows;
      bi_store.widget_builder_config.chart.pivot_columns = updated_cols;
      bi_store.widget_builder_config.chart.pivot_values = updated_vals;
    }
  }

  const { pivot_rows = [], pivot_columns = [], pivot_values = [] } = chart_config.value;

  const nested_headers = await generateNestedTableHeaders(
    state.data,
    pivot_columns,
    pivot_values,
    pivot_rows,
  );

  state.table_columns = nested_headers.slice(-1)[0].map(col => getTableColumn(col));
  state.nested_headers = nested_headers;

  const nested_data = await generateHandsontableData(
    state.data,
    pivot_columns,
    pivot_values,
    pivot_rows,
  );

  if (pivot_rows?.length)
    state.nested_rows = true;

  state.table_data = nested_data;
}

async function setupTable(reinitialize = true) {
  if (!props.isBuilder && props.isEditing && reinitialize) {
    const columns_map = state.columns.reduce((acc, col_key, index) => {
      const col_data = bi_store.widget_builder_config.chart.columns_map?.[col_key] || {};
      acc[col_key] = {
        key: col_key,
        width: get(col_data, 'width', null),
        visible: get(col_data, 'visible', true),
        order_index: get(col_data, 'order_index', index),
      };
      return acc;
    }, {});
    bi_store.widget_builder_config.chart.columns_map = cloneDeep(columns_map);
  }

  if (props.isBuilder) {
    state.table_columns = state.columns.map(col => getTableColumn(col));
  }
  else {
    state.table_columns = orderBy(
      state.columns.filter(col =>
        get(chart_config.value?.columns_map || {}, `${col}.visible`, true),
      ),
      col => get(chart_config.value?.columns_map || {}, `${col}.order_index`, 0),
    ).map(col => getTableColumn(col));
  }

  state.table_data = state.data;
  if (props.isBuilder) {
    state.column_config = state.columns.reduce((acc, col) => {
      const column_details = bi_store.alias_to_column_mapping()[col];
      if (column_details?.is_aggregation) {
        acc[col] = { backgroundColor: '#FFFAEB' };
      }
      return acc;
    }, {});
  }
}

async function initializeTable(reinitialize = true) {
  state.data = props.data;
  state.columns = (props.tableMetadata?.columns || [])?.map(col => col.name);

  if (chart_config.value?.type === 'pivot_table') {
    await setupPivotTable(reinitialize);
  }
  else {
    await setupTable(reinitialize);
  }
  bi_store.is_table_dirty = false;
}

async function generateNestedTableHeaders(data, columns, values, group_by_keys, delimiter = '|') {
  const nested_headers = [];

  // Handle case: no column pivoting
  if (columns.length === 0) {
    const flat_header_row = [];

    // Add row headers
    for (const rh of group_by_keys) {
      flat_header_row.push({ label: rh, key: rh, field: rh });
    }

    // Add values directly as columns
    for (const val of values) {
      flat_header_row.push({ label: val, key: val, field: val });
    }

    if (chart_config.value?.show_row_totals) {
      for (const val of values) {
        flat_header_row.push({ label: `Total ${val}`, key: `__row_total_${val}` });
      }
    }

    nested_headers.push(flat_header_row);
    return nested_headers;
  }

  // Build tree structure for column pivoting
  function buildTree(data, level = 0) {
    if (level >= columns.length)
      return [];

    const groups = {};
    for (const row of data) {
      const key = row[columns[level]];
      if (!groups[key])
        groups[key] = [];
      groups[key].push(row);
    }

    const nodes = [];
    for (const key in groups) {
      const children = buildTree(groups[key], level + 1);
      nodes.push({ label: key, children });
    }
    return nodes;
  }

  const tree = buildTree(data);

  function countLeaves(node) {
    if (!node.children || node.children.length === 0)
      return 1;
    return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
  }

  // Generate nested headers
  function fillHeaders(nodes, level = 0) {
    if (!nested_headers[level]) {
      nested_headers[level] = [];
      for (let i = 0; i < group_by_keys.length; i++) {
        nested_headers[level].push({ label: '', colspan: 1 });
      }
    }

    for (const node of nodes) {
      const leaf_count = countLeaves(node);
      nested_headers[level].push({
        label: node.label,
        colspan: leaf_count * values.length,
      });

      if (node.children.length > 0) {
        fillHeaders(node.children, level + 1);
      }
    }
  }

  fillHeaders(tree);

  const final_row = [];
  for (const rh of group_by_keys) {
    final_row.push({ label: rh, key: rh, field: rh });
  }

  function pushLeafLabels(nodes, path = []) {
    for (const node of nodes) {
      const currentPath = [...path, node.label];
      if (node.children.length > 0) {
        pushLeafLabels(node.children, currentPath);
      }
      else {
        for (const val of values) {
          final_row.push({
            label: val,
            key: currentPath.concat(val).join(delimiter),
            field: val,
          });
        }
      }
    }
  }

  pushLeafLabels(tree);
  if (chart_config.value?.show_row_totals) {
    for (let i = 1; i <= nested_headers.length; i++) {
      nested_headers[i - 1].push({ label: i === nested_headers.length ? 'Row Total' : '', colspan: values.length });
    }
    for (const val of values) {
      final_row.push({
        label: `Total ${val}`,
        key: `__row_total_${val}`,
      });
    }
  }
  nested_headers.push(final_row);
  return nested_headers;
}

function generateHandsontableData(data, columns, values, group_by_keys, delimiter = '|') {
  // Helper: group rows recursively
  function normalizeKey(val) {
    // Consistent key format for grouping — handles undefined/null gracefully
    return val === undefined || val === null ? '' : String(val);
  }

  function getRowKeys(row) {
    const column_key = columns.length
      ? columns.map(col => normalizeKey(row[col])).join(delimiter)
      : '';
    const row_keys = [];
    for (const val of values) {
      if (column_key.length) {
        row_keys.push({ data_key: `${column_key}${delimiter}${val}`, value_key: val });
      }
      else {
        row_keys.push({ data_key: val, value_key: val });
      }
    }
    return row_keys;
  }

  function formatRow(row, node = {}) {
    const row_keys = getRowKeys(row);
    for (const key of row_keys) {
      node[key.data_key] = row[key.value_key] ?? null;
      node[key.value_key] = node[key.data_key];
    }
    node.__actual_row = row;

    if (chart_config.value?.show_row_totals) {
      for (const val of values) {
        node[`__row_total_${val}`] = row[val] ?? null;
      }
    }

    return node;
  }

  function groupByHeaders(rows, headers) {
    if (headers.length === 0) {
      return rows.map(row => formatRow(row));
    }

    const [current, ...rest] = headers;
    const groups = {};

    rows.forEach((row) => {
      const key = normalizeKey(row[current]);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(row);
    });

    return Object.entries(groups).map(([key, grouped_rows]) => {
      const node = { [current]: key };

      if (rest.length > 0) {
        node.__is_group = true;
        node.__is_group_column = current;
        node.__children = groupByHeaders(grouped_rows, rest);

        if (chart_config.value?.show_column_totals || chart_config.value?.show_row_totals) {
          for (const row of grouped_rows) {
            if (chart_config.value?.show_column_totals) {
              const row_keys = getRowKeys(row);
              for (const key of row_keys) {
                const existing = Number(node[key.data_key] ?? 0);
                const current = Number(row[key.value_key] ?? 0);
                const sum = existing + (Number.isNaN(current) ? 0 : current);
                node[key.data_key] = String(sum);
                node.__is_column_total = true;
              }
            }

            if (chart_config.value?.show_row_totals) {
              for (const val of values) {
                const existing = Number(node[`__row_total_${val}`] ?? 0);
                const current = Number(row[val] ?? 0);
                const sum = existing + (Number.isNaN(current) ? 0 : current);
                node[`__row_total_${val}`] = String(sum);
              }
            }
          }
        }
      }
      else {
        node.__is_leaf_group = true;

        for (const row of grouped_rows) {
          const row_keys = getRowKeys(row);

          // aggregate value columns
          for (const key of row_keys) {
            const existing = Number(node[key.data_key] ?? 0);
            const current = Number(row[key.value_key] ?? 0);
            const sum = existing + (Number.isNaN(current) ? 0 : current);
            node[key.data_key] = String(sum);
            node[key.value_key] = String(sum);
          }

          // aggregate row totals if enabled
          if (chart_config.value?.show_row_totals) {
            for (const val of values) {
              const existing = Number(node[`__row_total_${val}`] ?? 0);
              const current = Number(row[val] ?? 0);
              const sum = existing + (Number.isNaN(current) ? 0 : current);
              node[`__row_total_${val}`] = String(sum);
            }
          }
        }
      }

      return node;
    });
  }

  const nested_data = groupByHeaders(data, group_by_keys);

  // --- Grand Total Column ---
  if (chart_config.value?.show_grand_totals) {
    const grand_total = { [group_by_keys[0] || 'Total']: 'Grand Totals', __is_grand_total: true };

    for (const row of data) {
    // aggregate per-column values
      const row_keys = getRowKeys(row);
      for (const key of row_keys) {
        const existing = Number(grand_total[key.data_key] ?? 0);
        const current = Number(row[key.value_key] ?? 0);
        grand_total[key.data_key] = existing + (Number.isNaN(current) ? 0 : current);
        grand_total[key.data_key] = String(grand_total[key.data_key]);
      }

      // aggregate per-row totals
      for (const val of values) {
        const existing = Number(grand_total[`__row_total_${val}`] ?? 0);
        const current = Number(row[val] ?? 0);
        grand_total[`__row_total_${val}`] = existing + (Number.isNaN(current) ? 0 : current);
        grand_total[`__row_total_${val}`] = String(grand_total[`__row_total_${val}`]);
      }
    }
    nested_data.push(grand_total);
  }

  return nested_data;
}

function getCellFormatting({ column, row_data, instance, value }) {
  // For total rows and columns styles
  if (row_data.__is_grand_total) {
    return {
      'background-color': '#475467',
      'font-weight': 600,
      'color': '#FFFFFF',
    };
  }
  if (row_data.__is_column_total || column?.data?.includes('__row_total_')) {
    return {
      'background-color': '#EAECF0',
      'font-weight': 600,
      'color': '#101828 !important',
    };
  }

  let rules = [];
  if (chart_config.value?.type) {
    rules = chart_config.value?.conditional_formatting || [];
  }
  const min_max_map = {};
  for (const rule of rules) {
    if (rule.formatting_style === 'single_color') {
      if (checkRuleMatch(rule, row_data, column, value)) {
        let color = rule.color;
        if (column.field !== rule.field) {
          color = getLighterColor(rule.color);
        }
        return {
          'background-color': color,
          'color': getContrastTextColor(color),
        };
      }
    }
    else {
      if (column.field !== rule.field)
        continue;
      let min = rule.start_range_value;
      let max = rule.end_range_value;
      if ((rule.start_range_at === 'min' || rule.end_range_at === 'max') && !min_max_map[column.field]) {
        min_max_map[rule.field] = getMinMax(instance, rule);
      }
      if (rule.start_range_at === 'min') {
        min = min_max_map[rule.field]?.min;
      }
      if (rule.end_range_at === 'max') {
        max = min_max_map[rule.field]?.max;
      }
      const cell_value = parseSafeNumber(value);
      if (Number.isNaN(cell_value) || (cell_value < min || cell_value > max))
        continue;
      const color = getColorForValue(cell_value, min, max, rule.color, rule.color_range);
      return {
        'background-color': color,
        'color': getContrastTextColor(color),
      };
    }
  }
  return null;
}

function parseSafeNumber(value) {
  if (value === null || value === undefined || value === '') {
    return Number.NaN;
  }
  const num = Number(value);
  return Number.isNaN(num) ? Number.NaN : num;
}

function getMinMax(instance, rule) {
  const columns = state.table_columns.map((col, index) => ({ index, field: col.field })).filter(col => col.field === rule.field);
  let values = [];
  for (const col of columns) {
    values = values.concat(instance.getDataAtCol(col.index).map(val => parseSafeNumber(val)).filter(val => !Number.isNaN(val)));
  }
  if (values.length) {
    return {
      min: Math.min(...values),
      max: Math.max(...values),
    };
  }
  return null;
}

function handleColumnResize(col_widths_map) {
  if (!props.isEditing)
    return;
  if (chart_config.value.type === 'table') {
    Object.entries(col_widths_map).forEach(([col_key, width]) => {
      if (bi_store.widget_builder_config.chart.columns_map?.[col_key]) {
      // Update width in the columns map
        bi_store.widget_builder_config.chart.columns_map[col_key] = {
          ...bi_store.widget_builder_config.chart.columns_map[col_key],
          width,
        };
      }
    });
  }
}

function autoFitColumns(hot) {
  nextTick(() => {
    const plugin = hot.getPlugin('autoColumnSize');
    plugin.recalculateAllColumnsWidth();
    const widths = [];
    for (let col = 0; col < hot.countCols(); col++) {
      if (chart_config.value?.type === 'table') {
        const column_key = state.table_columns[col].data;
        widths.push(chart_config.value?.columns_map?.[column_key]?.width || plugin.getColumnWidth(col));
      }
      else {
        widths.push(plugin.getColumnWidth(col));
      }
    }

    if (widths.length) {
      setTimeout(() => {
        hot.updateSettings({ colWidths: widths });
        hot.render();
      }, 100);
    }
  });
}

watch(() => (bi_store.is_table_dirty), () => {
  if (!props.isEditing)
    return;
  // state.changes_detected = true;
  initializeTable(false);
  state.force_update++;
});
// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  initializeTable();
});
</script>

<template>
  <div class="h-[100%] w-full">
    <div
      v-if="state.changes_detected"
      class="flex items-center justify-center w-full h-full"
    >
      <!-- Popup card -->
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 flex flex-col items-center min-w-[600px]">
        <HawkFeaturedIcon theme="light-circle-outline" size="md" color="primary" class="mb-4">
          <IconHawkInfoCircle />
        </HawkFeaturedIcon>

        <div class="text-sm font-semibold mb-1">
          {{ $t('Changes detected') }}
        </div>

        <div class="text-sm text-gray-600 mb-6">
          {{ $t('New changes is been detected. You can reload to view new changes.') }}
        </div>

        <HawkButton :loading="state.loading_changes" @click="fetchTableData(bi_store.is_table_dirty)">
          <IconHawkRefreshCwTwo />
          <span class="text-sm font-medium">
            {{ $t('Load Preview') }}
          </span>
        </HawkButton>
      </div>
    </div>
    <div v-else-if="!state.table_data?.length">
      <div class="flex items-center justify-center w-full h-full">
        <HawkIllustrations type="no-data" for="bi-table" />
      </div>
    </div>
    <BiHandsontable
      v-else
      :key="state.force_update"
      :bi-table-id="id"
      :height="table_height"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="getCellFormatting"
      :row-headers="enable_row_headers"
      class="h-full border"
      :enable-column-sorting="chart_config.type !== 'pivot_table'"
      @table-instance="state.table_instance = $event"
      @after-load-data="autoFitColumns"
      @column-resize="handleColumnResize"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
