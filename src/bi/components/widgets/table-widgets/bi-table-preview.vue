<script setup>
import { onMounted, watch } from 'vue';
import BiHandsontable from '~/bi/components/widgets/table-widgets/bi-handsontable.vue';
import { useBiTablePreview } from '~/bi/composables/bi-table-preview.composable.js';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  id: {
    type: String,
    default: () => crypto.randomUUID(),
  },
  data: {
    type: Array,
    default: null,
  },
  tableMetadata: {
    type: Object,
    default: () => ({}),
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  isBuilder: {
    type: Boolean,
    default: true,
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  height: {
    type: Number,
    default: 0,
  },
});

const bi_store = useBiStore();

const {
  state,
  chart_config,
  enable_row_headers,
  table_height,
  getCellFormatting,
  handleColumnResize,
  autoFitColumns,
  initializeTable,
  fetchTableData,
} = useBiTablePreview(props, bi_store);

// -------------------------------- Watchers -------------------------------- //
watch(() => (bi_store.is_table_dirty), () => {
  if (!props.isEditing)
    return;
  // state.changes_detected = true;
  initializeTable(false);
  state.force_update++;
});

// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  initializeTable();
});
</script>

<template>
  <div class="h-[100%] w-full">
    <div
      v-if="state.changes_detected"
      class="flex items-center justify-center w-full h-full"
    >
      <!-- Popup card -->
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 flex flex-col items-center min-w-[600px]">
        <HawkFeaturedIcon theme="light-circle-outline" size="md" color="primary" class="mb-4">
          <IconHawkInfoCircle />
        </HawkFeaturedIcon>

        <div class="text-sm font-semibold mb-1">
          {{ $t('Changes detected') }}
        </div>

        <div class="text-sm text-gray-600 mb-6">
          {{ $t('New changes is been detected. You can reload to view new changes.') }}
        </div>

        <HawkButton :loading="state.loading_changes" @click="fetchTableData()">
          <IconHawkRefreshCwTwo />
          <span class="text-sm font-medium">
            {{ $t('Load Preview') }}
          </span>
        </HawkButton>
      </div>
    </div>
    <div v-else-if="!state.table_data?.length">
      <div class="flex items-center justify-center w-full h-full">
        <HawkIllustrations type="no-data" for="bi-table" />
      </div>
    </div>
    <BiHandsontable
      v-else
      :key="state.force_update"
      :bi-table-id="id"
      :height="table_height"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="getCellFormatting"
      :row-headers="enable_row_headers"
      class="h-full border"
      :enable-column-sorting="chart_config.type !== 'pivot_table'"
      @table-instance="state.table_instance = $event"
      @after-load-data="autoFitColumns"
      @column-resize="handleColumnResize"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
