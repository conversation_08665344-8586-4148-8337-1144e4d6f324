<script setup>
import jsPDF from 'jspdf';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onUnmounted, reactive } from 'vue';
import { useModal } from 'vue-final-modal';
import BiCreateWidget from '~/bi/components/bi-create-widget.vue';
import BiHandsontable from '~/bi/components/widgets/table-widgets/bi-handsontable.vue';
import BiTablePreview from '~/bi/components/widgets/table-widgets/bi-table-preview.vue';
import { useBiStore } from '~/bi/store/bi.store';
import { generateChart } from '~/bi/utils/bi-helper.utils.js';
import { sleep } from '~/common/utils/common.utils';
import { load_js_css_file } from '~/common/utils/load-script.util';

const props = defineProps({
  widgetId: {
    type: String,
    required: true,
  },
  widgetName: {
    type: String,
    required: true,
  },
  config: {
    type: Object,
    required: true,
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
  layoutConfig: {
    type: Object,
    required: true,
  },
});

const bi_store = useBiStore();
const { dashboards, selected_dashboard, all_widget_details } = storeToRefs(bi_store);

const state = reactive({
  resize_observer: null,
  data: [],
  is_loading: false,
  table_metadata: {},
});

let chart_instance = null;

const edit_widget = useModal({
  component: BiCreateWidget,
  attrs: {
    onClose() {
      edit_widget.close();
    },
  },
});

const move_options = computed(() => {
  return Object.values(dashboards.value).filter(dashboard => dashboard.uid !== selected_dashboard.value).map(dashboard => ({
    label: dashboard.name,
    on_click: () => moveWidget(dashboard.uid),
  }));
});

const download_options = computed(() => {
  return [
    {
      label: 'Export as PDF',
      on_click: async () => {
        const imgData = await getImage();

        const img = new Image();
        img.onload = () => {
          const margin = 20;
          const pdfWidth = img.width + (margin * 2);
          const pdfHeight = img.height + (margin * 2);

          // eslint-disable-next-line new-cap
          const pdf = new jsPDF({
            orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
            unit: 'px',
            format: [pdfWidth, pdfHeight],
          });
          pdf.addImage(imgData, 'PNG', margin, margin, img.width, img.height);
          pdf.save(`${props.widgetName}.pdf`);
        };
        img.src = imgData;
      },
    },
    {
      label: 'Export as CSV',
      on_click: () => {
        logger.log('Export as CSV');
      },
    },
    {
      label: 'Export as PNG',
      on_click: async () => {
        const imgData = await getImage();
        const a = document.createElement('a');
        a.href = imgData;
        a.download = `${props.widgetName}.png`;
        a.click();
      },
    },
  ];
});

async function getImage() {
  const element = document.getElementById(props.widgetId);
  const echarts_canvas = element.querySelector('canvas');
  if (echarts_canvas) {
    return chart_instance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#ffffff',
    });
  }
  else {
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js',
      'html2canvas-js',
      'js',
    );
    return html2canvas(element, {
      scale: 2,
      backgroundColor: '#ffffff',
      useCORS: true,
      allowTaint: true,
      imageTimeout: 0,
      onclone: (document) => {
        const allElements = document.querySelectorAll('*');
        Array.from(allElements).forEach((element) => {
          element.style.overflow = 'visible';
          element.style.textOverflow = 'clip';
        });
      },
    }).then((canvas) => {
      const imgData = canvas.toDataURL('image/png');
      return imgData;
    });
  }
}

function editData(view) {
  edit_widget.patchOptions({
    attrs: {
      mode: 'edit',
      view,
      widgetName: props.widgetName,
      config: cloneDeep(props.config),
      widgetData: state.data,
      widgetId: props.widgetId,
      onClose() {
        edit_widget.close();
      },
    },
  });
  edit_widget.open();
}

async function duplicateWidget() {
  const widget_id = crypto.randomUUID();
  all_widget_details.value[widget_id] = {
    config: props.config,
    name: `${props.widgetName} - Copy`,
  };

  const current_widgets = dashboards.value[selected_dashboard.value].widgets;
  const unique_i = String(Math.max(...current_widgets.map(w => Number.parseInt(w.i) || 0)) + 1);

  const new_x = props.layoutConfig.x;
  const new_y = props.layoutConfig.y + props.layoutConfig.h;

  current_widgets.forEach((widget) => {
    if (widget.y >= new_y) {
      widget.y += props.layoutConfig.h;
    }
  });

  dashboards.value[selected_dashboard.value].widgets.push({
    x: new_x,
    y: new_y,
    w: props.layoutConfig.w,
    h: props.layoutConfig.h,
    i: unique_i,
    widget_id,
  });

  await sleep(500);
  const el = document.querySelectorAll(`[widget_id='${widget_id}']`)?.[0];
  if (el) {
    el.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    });
  }
}

async function moveWidget(dashboard_uid) {
  const current_widgets = dashboards.value[selected_dashboard.value].widgets;
  const widget = current_widgets.find(widget => widget.widget_id === props.widgetId);
  widget.x = 0;
  widget.y = 0;
  current_widgets.forEach((existing_widget) => {
    if (widget.y >= existing_widget.y) {
      widget.y += existing_widget.h;
    }
  });
  dashboards.value[selected_dashboard.value].widgets = current_widgets.filter(w => w.widget_id !== props.widgetId);
  dashboards.value[dashboard_uid].widgets.push(widget);
}

function deleteWidget() {
  const current_widgets = dashboards.value[selected_dashboard.value].widgets;
  dashboards.value[selected_dashboard.value].widgets = current_widgets.filter(widget => widget.widget_id !== props.widgetId);
}

watch(() => props.config, async () => {
  if (!props.widgetId || !props.config)
    return;
  state.is_loading = true;
  await nextTick();
  if (!all_widget_details.value[props.widgetId].data) {
    const data = await bi_store.getWidgetData(props.config.query.selected_database, props.config.query.stages);
    all_widget_details.value[props.widgetId].data = data.data;
    all_widget_details.value[props.widgetId].table_metadata = data.metadata;
    state.data = data.data;
    state.table_metadata = data.metadata;
  }
  else {
    state.data = all_widget_details.value[props.widgetId].data;
    state.table_metadata = all_widget_details.value[props.widgetId].table_metadata;
  }
  state.is_loading = false;
  if (props.config.chart.type === 'table') {
    // Probably some data processing
  }
  else {
    chart_instance = await generateChart(props.widgetId, state.data, props.config.chart, chart_instance);

    const chartElement = document.getElementById(props.widgetId);
    state.resize_observer = new ResizeObserver(() => {
      // For echarts
      chart_instance?.resize?.();
      // For fusion charts
      const { width, height } = chartElement.getBoundingClientRect();
      chart_instance?.resizeTo?.(width, height);
    });
    state.resize_observer.observe(chartElement);
  }
}, { deep: true, immediate: true });

onUnmounted(() => {
  const chartElement = document.getElementById(props.widgetId);
  if (chartElement) {
    state.resize_observer?.unobserve(chartElement);
  }
  chart_instance?.dispose();
});
</script>

<template>
  <div class="p-3 h-full w-full group">
    <div class="h-6 flex items-center justify-between gap-3">
      <div class="w-full text-sm font-medium text-gray-900 text-ellipsis overflow-hidden whitespace-nowrap">
        {{ props.widgetName }}
      </div>
      <div v-if="props.isEditing" class="items-center gap-3 group-hover:flex hidden">
        <IconHawkEditFive
          v-tippy="{
            content: 'Edit data',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="editData('data-builder')"
        />
        <IconHawkBarChartTen
          v-tippy="{
            content: 'Edit chart',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="editData('chart-builder')"
        />
        <IconHawkCopyFour
          v-tippy="{
            content: 'Duplicate',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="duplicateWidget"
        />
        <HawkMenu additional_trigger_classes="!ring-0 !border-0 mt-1.5" :items="move_options">
          <template #trigger>
            <IconHawkLogOutTwo
              v-tippy="{
                content: 'Move',
                placement: 'top',
              }"
              class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
            />
          </template>
        </HawkMenu>
        <IconHawkTrashThree
          v-tippy="{
            content: 'Delete',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="deleteWidget"
        />
      </div>
      <div v-else class="items-center gap-3 group-hover:flex hidden">
        <HawkMenu additional_trigger_classes="!ring-0 !border-0 mt-1.5" :items="download_options">
          <template #trigger>
            <IconHawkDotsVertical
              class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
            />
          </template>
        </HawkMenu>
      </div>
    </div>
    <HawkLoader v-if="state.is_loading" />
    <div v-if="['table', 'pivot_table'].includes(props.config.chart.type)" class="overflow-hidden">
      <BiTablePreview
        v-if="!state.is_loading"
        :id="props.widgetId"
        :is-builder="false"
        :config="props.config"
        :data="state.data"
        :table-metadata="state.table_metadata"
        :height="props.layoutConfig.h * 30"
        :is-editing="false"
      />
    </div>
    <div v-else :id="props.widgetId" class="h-[calc(100%-24px)] w-full" />
  </div>
</template>
