<script setup>
import { ref } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

defineOptions({ name: 'BiQueryBuilderColumnsHierarchy' });

const props = defineProps({
  node: { type: Object, required: true },
  table: { type: Object, required: true },
  depth: { type: Number, default: 0 },
  hasFunctions: { type: Boolean, default: true },
  hoveredColumn: { type: Object },
});

const emit = defineEmits(['hover', 'select']);

const { getIconsForType, getOperatorsForType } = useBIQueryBuilder();

const open = ref(false);

function toggle() {
  open.value = !open.value;
}

function onHover(e) {
  emit('hover', { e, column: props.node, table: props.table });
}

function onSelect() {
  if (props.node.is_hierarchy) {
    toggle();
    return;
  }
  emit('select', { column: props.node, table: props.table });
}
</script>

<template>
  <div>
    <div
      v-if="node.is_hierarchy"
      class="flex items-center justify-between gap-2"
      :class="`pl-${2 + depth} py-2 cursor-pointer group hover:bg-gray-50 rounded-lg relative`"
      @click.stop="toggle"
    >
      <div class="flex items-center">
        <IconHawkChevronRight :class="open ? 'rotate-90' : ''" class="text-gray-600 size-4 mr-2 transition-transform" />
        <span class="text-gray-700 text-sm font-medium">{{ node.label }}</span>
      </div>
    </div>

    <div
      v-else
      class="flex items-center justify-between gap-2 pl-6 py-2 cursor-pointer group hover:bg-gray-50 rounded-lg relative"
      @click="onSelect"
    >
      <div class="flex items-center">
        <component :is="getIconsForType(node.type)" class="text-gray-600 size-4 mr-2" />
        <span class="text-gray-700 text-sm font-medium">{{ node.label }}</span>
      </div>
      <hawk-button v-if="(getOperatorsForType(node.type).length && hasFunctions)" icon size="xxs" type="light" color="gray" class="text-gray-500 size-4 mr-2 -mt-1  group-hover:visible" :class="hoveredColumn?.alias === node.alias ? 'visible' : 'invisible'" @click.stop="onHover($event)">
        <IconHawkChevronRight />
      </hawk-button>
    </div>

    <div v-if="node.is_hierarchy && open" class="pl-2">
      <BiQueryBuilderColumnsHierarchy
        v-for="child in node.children"
        :key="child.label"
        :node="child"
        :table="table"
        :depth="depth + 1"
        :has-functions="hasFunctions"
        :hovered-column="hoveredColumn"
        @hover="$emit('hover', $event)"
        @select="$emit('select', $event)"
      />
    </div>
  </div>
</template>
