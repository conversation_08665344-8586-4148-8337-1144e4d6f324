<script setup>
import { computed, ref, watch } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import BiQueryBuilderColumnsHierarchy from './bi-query-builder-columns-hierarchy.vue';

const props = defineProps({
  tables: {
    type: Array,
    default: () => ([]),
  },
  stageIndex: {
    type: Number,
  },
  isDropdown: {
    type: Boolean,
    default: true,
  },
  hasFunctions: {
    type: Boolean,
    default: true,
  },
  isFilters: {
    type: Boolean,
    default: false,
  },
  slotContainerClasses: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['selected', 'expression']);
const { $t } = useCommonImports();
const { getIconsForType, getOperatorsForType, constructColumnAlias } = useBIQueryBuilder();
const bi_store = useBiStore();

const date_bins = [{ label: $t('Week'), value: 'week' }, { label: $t('Month'), value: 'month' }, { label: $t('Quarter'), value: 'quarter' }, { label: $t('Year'), value: 'year' }];
const bin_count = [{ label: '10', value: 10 }, { label: '100', value: 100 }, { label: '500', value: 500 }, { label: '1000', value: 1000 }, { label: '1500', value: 1500 }, { label: '2000', value: 2000 }, { label: '2500', value: 2500 }];

const dropdown = ref(null);
const dropdown_content = ref(null);
const sub_menu = ref(null);
const search_input = ref('');
const search_operator_input = ref('');
const hovered_column = ref(null);
const hovered_table = ref(null);
const hovered_column_operators = computed(() => props.hasFunctions ? getOperatorsForType(hovered_column.value?.type) : []);
const hovered_column_operators_with_search = computed(() => hovered_column_operators.value.filter(operator => operator.label.toLowerCase().includes(search_operator_input.value.toLowerCase())));

// Collapsible state for tables in the dropdown
const expanded_tables = ref({});

function tableKey(table, idx) {
  return table?.uid ?? `${table?.label ?? 'table'}_${idx}`;
}

function isTableOpen(table, idx) {
  return !!expanded_tables.value[tableKey(table, idx)];
}

function toggleTable(table, idx) {
  const key = tableKey(table, idx);
  expanded_tables.value[key] = !expanded_tables.value[key];
}

// Compute filtered tables
const filtered_tables = computed(() => {
  return filterColumns(props.tables, search_input.value || '');
});

// Expand all tables by default (and keep newly filtered tables expanded)
watch(
  () => filtered_tables.value,
  (newTables) => {
    const map = {};
    newTables.forEach((table, idx) => {
      map[tableKey(table, idx)] = true;
    });
    expanded_tables.value = { ...map, ...expanded_tables.value };
  },
  { immediate: true },
);

const date_bins_with_search = computed(() => date_bins.filter(bin => bin.label?.toLowerCase()?.includes(search_operator_input.value.toLowerCase())));
const bin_count_with_search = computed(() => bin_count.filter(bin => bin.label?.toLowerCase()?.includes(search_operator_input.value.toLowerCase())));

// Function to detect if the dropdown is at the bottom of the page
function isDropdownAtBottom() {
  if (dropdown.value) {
    const dropdownRect = dropdown.value?.getBoundingClientRect();
    const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const dropdownHeight = dropdownContentRect.height;
    return windowHeight - dropdownRect.top < dropdownHeight;
  }
  return false;
}

// Filter the columns based on the search input and return the list of new tables that have columns that match the search input
// It should return only the columns that are matching the search input
function filterColumns(tables, search_input) {
  // If search is empty, return original tables
  if (!search_input)
    return tables;

  const q = search_input.toLowerCase();

  // Recursively filter a node (leaf or hierarchy). Returns a new node or null.
  function filterNode(node) {
    // hierarchy node: try to filter children recursively
    if (node.is_hierarchy && Array.isArray(node.children)) {
      const filteredChildren = node.children.map(filterNode).filter(Boolean);
      // If any child matches, keep node with those children
      if (filteredChildren.length) {
        return { ...node, children: filteredChildren };
      }
      // If the hierarchy node's label itself matches, keep the whole subtree
      if (node.label && node.label.toLowerCase().includes(q)) {
        return { ...node };
      }
      // otherwise exclude
      return null;
    }

    // leaf node: include if label matches
    if (node.label && node.label.toLowerCase().includes(q)) {
      return node;
    }
    return null;
  }

  const new_tables = tables.map((table) => {
    const columns = table.columns.map(col => filterNode(col)).filter(Boolean);
    return { ...table, columns };
  });

  const filtered_tables = new_tables.filter(table => table.columns.length > 0);

  // If nothing matched, return original tables (preserve existing UI)
  return filtered_tables.length ? filtered_tables : tables;
}

function onColumnHovered(e, column, table) {
  hovered_column.value = column;
  hovered_table.value = table;
  search_operator_input.value = '';
  // Set the sub menu position of the top based on the mouse position and the dropdown location in the screen
  const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
  const rect = e.srcElement.getBoundingClientRect();
  // If the position is at the bottom of the screen, set the top position to the bottom of the dropdown
  sub_menu.value.style.left = `${rect.left + rect.width + 10}px`;
  if ((e.clientY + dropdownContentRect.height) > window.innerHeight) {
    sub_menu.value.style.bottom = `${window.innerHeight - rect.bottom + 5}px`;
    sub_menu.value.style.top = 'auto';
  }
  // If the position is at the top of the screen, set the top position to the top of the dropdown
  else {
    sub_menu.value.style.top = `${rect.top - 50}px`;
    sub_menu.value.style.bottom = 'auto';
  }
}

function onOperatorClicked(operator) {
  emit('selected', { ...hovered_column.value, type: operator.output_type, agg: operator.name, is_aggregation: true, table_name: hovered_table.value.label === bi_store.widget_builder_config.query.selected_table.label ? null : hovered_table.value.label, alias: constructColumnAlias({ table_name: hovered_table.value.label === bi_store.widget_builder_config.query.selected_table.label ? null : hovered_table.value.label, column_name: hovered_column.value.label, agg: operator.label }) });
}

function onColumnClicked(column, table) {
  emit('selected', { ...column, table_name: table.label, alias: column.alias, is_table_column: true });
}

function onExpressionClicked() {
  emit('expression');
}

function onDateBinClicked(bin, bin_label) {
  emit('selected', { ...hovered_column.value, bin, is_bin: true, table_name: hovered_table.value.label === bi_store.widget_builder_config.query.selected_table.label ? null : hovered_table.value.label, alias: constructColumnAlias({ table_name: hovered_table.value.label === bi_store.widget_builder_config.query.selected_table.label ? null : hovered_table.value.label, column_name: hovered_column.value.label, bin: bin_label }) });
}
</script>

<template>
  <div ref="dropdown" class="bi-columns-dropdown relative z-20" :class="{ 'h-10': isDropdown }">
    <div ref="dropdown_content" class="shadow-lg border border-gray-200 rounded-lg  w-full flex flex-col bg-white" :class="{ 'bottom-20': isDropdownAtBottom(), 'top-0': !isDropdownAtBottom(), 'absolute': isDropdown }">
      <div class="relative">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <IconHawkSearch class="text-gray-600 size-4" />
        </div>
        <input
          v-model="search_input"
          name="input"
          autocomplete="off"
          :placeholder="$t('Search for a field')"
          class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 text-sm sm:leading-6 rounded-lg"
        >
      </div>
      <div class="bi-columns-dropdown-content min-h-[300px] max-h-80 overflow-auto scrollbar border-t">
        <div v-for="(table, tIndex) in filtered_tables" :key="`${table.label}_${tIndex}`">
          <div v-if="hasFunctions" class="text-sm text-gray-700 font-semibold flex items-center gap-1 px-3 py-2 cursor-pointer select-none" @click="toggleTable(table, tIndex)">
            <IconHawkDatabaseTwo class="size-4" />
            <span class="ml-2 flex-1">{{ table.label || (`${$t('Stage')} ${stageIndex}`) }}</span>
            <IconHawkChevronRight class="size-4 transition-transform" :class="{ 'rotate-90': isTableOpen(table, tIndex) }" />
          </div>
          <div v-show="isTableOpen(table, tIndex)">
            <BiQueryBuilderColumnsHierarchy
              v-for="column in (table.columns_with_hierarchy || table.columns)"
              :key="column.label"
              :node="column"
              :table="table"
              :hovered-column="hovered_column"
              :has-functions="hasFunctions"
              @hover="payload => onColumnHovered(payload.e, payload.column, payload.table)"
              @select="payload => onColumnClicked(payload.column, payload.table)"
              @mouseenter="hovered_column = null;"
            />
          </div>
        </div>
      </div>
      <div v-if="hasFunctions" class="border-t">
        <span type="text" class="text-sm font-medium flex py-2 px-1 text-primary-700 hover:bg-gray-50 cursor-pointer rounded-lg flex items-center" @click="onExpressionClicked">
          <component :is="getIconsForType('formula')" class=" size-4 mx-2" />
          {{ $t('Custom Expression') }}
        </span>
      </div>
    </div>
    <div v-show="isFilters ? hovered_column : hovered_column_operators?.length" ref="sub_menu" class="dropdown-sub-menu shadow-lg border border-gray-200 rounded-lg fixed w-[300px] min-h-[300px] left-[340px] -mb-8 bg-white z-20" :class="[slotContainerClasses]">
      <slot :column="hovered_column" :table="hovered_table">
        <div class="relative">
          <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <IconHawkSearch class="text-gray-600 size-4" />
          </div>
          <input
            v-model="search_operator_input"
            name="input"
            autocomplete="off"
            :placeholder="$t('Search options')"
            class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 text-sm sm:leading-6 rounded-lg"
          >
        </div>
        <div class="overflow-auto border-t scrollbar h-[300px] pl-3 pt-2">
          <div v-if="hovered_column?.type === 'date' || hovered_column?.type === 'integer'" class="gap-2">
            <span class="text-sm text-gray-900 font-semibold">
              {{ $t('Binning') }}
            </span>
            <div v-if="(hovered_column?.type === 'date' ? date_bins_with_search : bin_count_with_search)?.length === 0">
              <div class=" gap-2 pl-6 py-2 rounded-lg relative text-gray-400 text-sm font-medium flex items-center text-center">
                {{ $t('No results found') }}
              </div>
            </div>
            <div v-for="bin in (hovered_column?.type === 'date' ? date_bins_with_search : bin_count_with_search)" :key="bin.value" class="flex  items-center gap-2 pl-6 py-2 cursor-pointer hover:bg-gray-50 rounded-lg relative" @click="onDateBinClicked(bin.value, bin.label)">
              <div class="text-gray-700 text-sm font-medium">
                {{ bin.label }}
              </div>
            </div>
          </div>
          <span class="text-sm text-gray-900 font-semibold">
            {{ $t('Aggregations') }}
          </span>
          <div v-if="!hovered_column_operators_with_search.length">
            <div class=" gap-2 pl-6 py-2 rounded-lg relative text-gray-400 text-sm font-medium flex items-center text-center">
              {{ $t('No results found') }}
            </div>
          </div>
          <div v-for="operator in hovered_column_operators_with_search" :key="operator.label" class="flex items-center gap-2 pl-6 py-2 cursor-pointer hover:bg-gray-50 rounded-lg relative" @click="onOperatorClicked(operator)">
            <span class=" text-gray-700 text-sm font-medium">{{ operator.label }}</span>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>
