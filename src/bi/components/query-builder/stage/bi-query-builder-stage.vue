<script setup>
// import { useModal } from 'vue-final-modal';
// import biQueryBuilderFieldSettings from '~/bi/components/query-builder/bi-query-builder-field-settings.vue';

import BiFilterList from '~/bi/components/filters/bi-filter-list/bi-filter-list.vue';
import BIFilterTrigger from '~/bi/components/filters/bi-filter-trigger.vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
  modelValue: {
    type: Object,
    default: () => ({
      columns: [],
      orderBy: [],
      limit: null,
    }),
  },
});

const emit = defineEmits(['update:modelValue', 'fieldsDeleted', 'fieldsAdded']);

const has_row_limit = ref(false);
const is_expression_dropdown_open = ref(false);
const { getIconsForType } = useBIQueryBuilder();
const is_dropdown_open = ref(props.modelValue.columns.length === 0);
const { $t } = useCommonImports();

const draggable = defineAsyncComponent(() => import('vuedraggable'));

const localColumns = computed({
  get: () => props.modelValue.columns || [],
  set: v => updateModelValue({ columns: v }),
});

const localOrderBy = computed({
  get: () => props.modelValue.orderBy || [],
  set: v => updateModelValue({ orderBy: v }),
});

const localFilters = computed({
  get: () => props.modelValue.filters || [],
  set: v => updateModelValue({ filters: v }),
});

// const { open: openFieldSettings, close: closeFieldSettings, patchOptions } = useModal({
//   component: biQueryBuilderFieldSettings,
// });

// function updateSettings(field) {
//   patchOptions({
//     attrs: {
//       field,
//       onClose() {
//         closeFieldSettings();
//       },
//       onSave() {
//         closeFieldSettings();
//         emit('fieldsAdded');
//       },
//     },
//   });
//   openFieldSettings();
// }

function onColumnsSelected(field) {
  if (props.modelValue.columns.find(f => f.expression ? false : (f.alias === field.alias)))
    return;
  updateModelValue({ columns: [...props.modelValue.columns, field] });
  emit('fieldsAdded');
}

function removeField(index) {
  const remaining_fields = props.modelValue.columns.filter((_, i) => i !== index);
  updateModelValue({ columns: remaining_fields });
  emit('fieldsDeleted');
}

function saveExpression(expression) {
  if (props.modelValue.columns.find(f => f.expression === expression.expression))
    return;
  updateModelValue({ columns: [...props.modelValue.columns, expression] });
  is_expression_dropdown_open.value = false;
  emit('fieldsAdded');
}

function onSortSelected(field) {
  if (props.modelValue.orderBy.find(f => f.alias === field.alias))
    return;
  field.direction = field.direction === 'asc' ? 'desc' : 'asc';
  updateModelValue({ orderBy: [...props.modelValue.orderBy, field] });
}

function onFilterApply(filter_config) {
  updateModelValue({ filters: [...props.modelValue.filters, filter_config] });
}

function removeFilterField(index) {
  updateModelValue({ filters: props.modelValue.filters.filter((_, i) => i !== index) });
}

function editAppliedFilter(filter_config, index, close = () => {}) {
  const updated_filter_fields = [...props.modelValue.filters];
  updated_filter_fields[index] = filter_config;
  updateModelValue({ filters: updated_filter_fields });
  close?.();
}

function removeSortField(index) {
  updateModelValue({ orderBy: props.modelValue.orderBy.filter((_, i) => i !== index) });
}

function updateModelValue({ columns, orderBy, limit, filters }) {
  const limit_value = limit === undefined ? props.modelValue.limit : limit;
  emit('update:modelValue', {
    columns: columns || props.modelValue.columns,
    orderBy: orderBy || props.modelValue.orderBy,
    filters: filters || props.modelValue.filters,
    limit: has_row_limit.value ? limit_value : null,
  });
}
</script>

<template>
  <div class="w-full">
    <div>
      <draggable v-model="localColumns" item-key="alias" tag="div" handle=".move" :animation="150">
        <template #item="{ element: field, index }">
          <div :key="field.label" class="flex items-center justify-between gap-2 py-2 cursor-pointer hover:bg-gray-50 rounded-lg relative group">
            <div class="flex items-center">
              <div class="pr-3 move cursor-move invisible group-hover:visible">
                <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
              </div>
              <span v-if="field.expression && field.same_stage" :class="{ 'border-l-4 border-warning-300 -ml-1': field.aggregation_type === 'aggregation' }">
                <component :is="getIconsForType(field.aggregation_type === 'aggregation' ? 'function' : 'formula')" class="text-gray-600 size-4 mr-2" />
              </span>
              <span v-else :class="{ 'border-l-4 border-warning-300 -ml-1': field.agg }">
                <component :is="getIconsForType(field.agg ? 'function' : field.type)" class="text-gray-600 size-4 mr-2" />
              </span>
              <span class="text-sm text-gray-700">{{ field.alias }}</span>
            </div>
            <div class="flex items-center">
              <!-- <div v-if="field.expression" class="text-gray-500 hidden group-hover:block mr-2" @click="updateSettings(field)">
                <IconHawkSettingsOne class="size-4" />
              </div> -->
              <div class="text-gray-500 hidden group-hover:block mr-2" @click="removeField(index)">
                <IconHawkXClose class="size-4" />
              </div>
            </div>
          </div>
        </template>
      </draggable>
      <div v-click-outside="() => is_dropdown_open = false">
        <Hawk-button type="link" @click="is_dropdown_open = true">
          <IconHawkPlus />   {{ $t('Add columns') }}
        </Hawk-button>
        <div v-if="is_dropdown_open" class="fixed min-w-[300px] z-20">
          <bi-query-builder-columns-dropdown :tables="tables" :stage-index="stageIndex" @selected="onColumnsSelected" @expression="is_expression_dropdown_open = true;is_dropdown_open = false;" />
        </div>
      </div>
      <div v-if="modelValue.orderBy.length > 0" class="border-t py-2 mt-2">
        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center">
            <IconHawkSwitchVerticalOne class="mr-2" />
            {{ $t('Sort') }}
          </div>
          <bi-query-builder-columns-dropdown-button :tables="tables" :fields="modelValue.columns" @selected="onSortSelected">
            <Hawk-button size="xxs" color="gray" type="light" icon>
              <IconHawkPlus class="size-4" />
            </Hawk-button>
          </bi-query-builder-columns-dropdown-button>
        </div>
        <draggable v-model="localOrderBy" item-key="label" tag="div" handle=".move" :animation="150">
          <template #item="{ element: field, index }">
            <div :key="field.label" class="flex items-center justify-between gap-2 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative group" @click="field.direction = field.direction === 'asc' ? 'desc' : 'asc'">
              <div class="flex items-center">
                <div class="pr-3 move cursor-move invisible group-hover:visible">
                  <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
                </div>
                <component :is="getIconsForType(field.direction || 'asc')" class="text-gray-600 size-4 mr-2" />
                <span class="text-sm text-gray-700">{{ field.label }}</span>
              </div>
              <div class="text-gray-500 hidden group-hover:block mr-2" @click.stop="removeSortField(index)">
                <IconHawkXClose class="size-4" />
              </div>
            </div>
          </template>
        </draggable>
      </div>
      <!-- Filter List -->
      <div v-if="modelValue.filters.length > 0" class="border-t py-2 mt-2">
        <!-- {{ modelValue.filters }} -->

        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center">
            <IconHawkFilterFunnelOne class="mr-2" />
            {{ $t('Filter') }}
          </div>
          <BIFilterTrigger :tables="tables" @apply="onFilterApply">
            <template #trigger_label>
              <Hawk-button size="xxs" color="gray" type="light" icon>
                <IconHawkPlus class="size-4" />
              </Hawk-button>
            </template>
          </BIFilterTrigger>
        </div>
        <BiFilterList :existing-column-filters="localFilters" @remove-filter-field="removeFilterField" @edit-applied-filter="editAppliedFilter" @reorder="(list) => updateModelValue({ filters: list })" />
      </div>
      <div v-if="has_row_limit" class="mt-2 p-2 border-t">
        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center w-[150px]">
            <IconHawkList class="mr-2" />
            {{ $t('Row Limit') }}
          </div>
          <input :value="modelValue.limit" type="text" placeholder="Enter row limit" class="block w-full rounded-lg border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-500 focus:ring-1 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 pl-4" @input="updateModelValue({ limit: $event.target.value })">
        </div>
      </div>
      <div>
        <div class="flex items-center justify-between">
          <div />
          <div class="flex items-center gap-2">
            <!-- Filter -->
            <BIFilterTrigger :tables="tables" @apply="onFilterApply">
              <template #trigger_label>
                <Hawk-button size="xxs" color="gray" type="light" class="border !border-gray-200" icon>
                  <IconHawkFilterFunnelOne class="size-3.5" />
                </Hawk-button>
              </template>
            </BIFilterTrigger>
            <bi-query-builder-columns-dropdown-button :tables="tables" :fields="modelValue.columns" :stage-index="stageIndex" @selected="onSortSelected">
              <Hawk-button size="xxs" color="gray" type="light" class="border !border-gray-200" icon>
                <IconHawkSwitchVerticalOne class="size-3.5" />
              </Hawk-button>
            </bi-query-builder-columns-dropdown-button>
            <Hawk-button size="xxs" :color="has_row_limit ? 'primary' : 'gray'" type="light" class="border !border-gray-200" icon @click="has_row_limit = !has_row_limit; updateModelValue({ limit: null })">
              <IconHawkList class="size-3.5" />
            </Hawk-button>
          </div>
        </div>
        <bi-query-builder-expression-editor v-if="is_expression_dropdown_open" :tables="tables" :fields="modelValue.columns" @save="saveExpression" @close="is_expression_dropdown_open = false" />
      </div>
    </div>
  </div>
</template>
