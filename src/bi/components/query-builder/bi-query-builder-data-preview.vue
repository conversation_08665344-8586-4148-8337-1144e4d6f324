<script setup>
import { onMounted, reactive, watch } from 'vue';
import BiBottomDrawer from '~/bi/components/common/bi-bottom-drawer.vue';
import BiTablePreview from '~/bi/components/widgets/table-widgets/bi-table-preview.vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const emit = defineEmits(['continue']);

const { copy } = useClipboard();
const bi_store = useBiStore();
const bi_query_builder = useBIQueryBuilder();

const state = reactive({
  is_copied: false,
  force_update: 0,
});

const editor = ref(null);
let build_query = null;
let knex_instance = null;
async function copyToClipboard() {
  copy(bi_store.widget_builder_config.query.sql_query);
  state.is_copied = true;
  setTimeout(() => {
    state.is_copied = false;
  }, 2000);
}

async function initializePackages() {
  const { EditorView, basicSetup } = (await import('codemirror'));
  const { sql, PostgreSQL } = await import('@codemirror/lang-sql');
  const { buildQuery } = await import('@sensehawk/query-generator');
  build_query = buildQuery;
  const knex_module = await import('https://cdn.jsdelivr.net/npm/knex-browser@3.0.6/dist/knex4.js'); // TODO package in Github instead of external js deliver file
  const knex = knex_module.default;
  knex_instance = knex({
    client: 'pg',
  });
  editor.value = new EditorView({
    doc: bi_store.widget_builder_config.query.sql_query || '',
    extensions: [
      basicSetup,
      EditorView.editable.of(false),
      EditorView.lineWrapping, // 👈 enables wrapping
      sql({
        dialect: PostgreSQL,
        upperCaseKeywords: true,
      }),
    ],
    parent: document.getElementById('sqlArea'),
  });
}

onMounted(() => {
  initializePackages();
});

watch(() => bi_store.config_change_detection_counter, async () => {
  const stage_config = bi_query_builder.getStageConfig(bi_store.widget_builder_config.query.stages);
  if (editor.value && build_query && knex_instance && stage_config) {
    const query = build_query(stage_config, knex_instance);
    const results = query.toString();
    bi_store.widget_builder_config.query.sql_query = results;
    editor.value.dispatch({
      changes: {
        from: 0,
        to: editor.value.state.doc.length,
        insert: results || '',
      },
    });
  }
});

const is_fetching_data = ref(false);
// async function configureChart() {
//   try {
//     is_fetching_data.value = true;
//     await bi_store.getDataFromStages();
//     emit('continue');
//   }
//   finally {
//     is_fetching_data.value = false;
//   }
// }

async function fetchTableData() {
  try {
    if (bi_store.widget_builder_config.query.stages[0]?.value?.columns?.length) {
      const widget_data = await bi_store.getWidgetData(bi_store.widget_builder_config.query.selected_database, bi_store.widget_builder_config.query.stages);
      bi_store.widget_data = widget_data.data;
      bi_store.table_metadata = widget_data.metadata;
      bi_store.sql_query = widget_data.sql_query;
    }
    else {
      bi_store.widget_data = [];
      bi_store.table_metadata = null;
      bi_store.sql_query = '';
    }
    state.force_update++;
  }
  catch (error) {
    logger.error(error);
  }
}

watch(() => (bi_store.config_change_detection_counter), () => {
  // state.changes_detected = true;
  fetchTableData();
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1 max-h-[calc(100vh-85px)] overflow-auto">
      <BiTablePreview
        :key="state.force_update"
        :is-builder="true"
        :config="{}"
        :data="bi_store.widget_data"
        :table-metadata="bi_store.table_metadata"
      />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton :loading="is_fetching_data" @click="emit('continue')">
        <IconHawkBarChartTen />
        Configure chart
        <IconHawkArrowRight />
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show SQL Query"
      hide-text="Hide SQL Query"
    >
      <template #default="{ height }">
        <div class="p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-1 text-sm font-semibold text-gray-700">
              <IconHawkDatabaseTwo />
              SQL Query
            </div>
            <div class="flex items-center gap-2">
              <button
                class="text-xs font-medium text-gray-700 flex items-center gap-1"
                @click="copyToClipboard"
              >
                <IconHawkCheckCircleGreen v-if="state.is_copied" class="w-4 h-4 text-green-600" />
                <IconHawkCopyOne v-else class="w-4 h-4" />
                {{ state.is_copied ? 'Copied!' : 'Copy' }}
              </button>
            </div>
          </div>
          <div class="overflow-auto scrollbar mt-4" :style="{ height: `${height - 80}px` }">
            <div id="sqlArea" class="w-full h-full sql-area" />
          </div>
        </div>
      </template>
    </BiBottomDrawer>
  </div>
</template>

<style lang="scss" scoped>
:deep(.sql-area) {
  .ͼ5 .cm-activeLine,.ͼ5 .cm-activeLineGutter{
    background-color: transparent !important;
  }
  .ͼ5 .cm-gutters{
    display:none !important;
  }
  .cm-content{
    padding:0 !important;
  }
}
</style>
