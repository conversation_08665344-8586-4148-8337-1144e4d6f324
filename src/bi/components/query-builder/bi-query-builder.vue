<script setup>
import { watchDebounced } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const { $t } = useCommonImports();
const bi_store = useBiStore();
const { getIconsForType, constructColumnAlias, createDefaultSelection } = useBIQueryBuilder();

const join_types = getIconsForType('joins');

const { widget_builder_config, all_tables } = storeToRefs(bi_store);
const { stages, selected_table } = toRefs(widget_builder_config.value.query);

watchDebounced(() => stages.value, () => {
  bi_store.config_change_detection_counter++;
}, { deep: true, debounce: 100 });

function getTablesFromStageFields(previous_stage) {
  const fieldData = field => ({
    label: field.alias,
    type: field.type,
    alias: field.alias,
  });

  const expressionData = field => ({
    label: field.label,
    type: field.type,
    expression: field.expression,
    alias: field.alias,
    fields: field.fields,
    aggregation_type: field.aggregation_type,
    function: field.function,
  });

  const columns = previous_stage.value.columns.map(field => ({
    ...(field.expression ? expressionData(field) : fieldData(field)),
  }));

  const tables = [{
    label: ``,
    columns,
  }];
  return tables;
}

function addStage() {
  const previous_stage = stages.value[stages.value.length - 1];
  const tables = getTablesFromStageFields(previous_stage);
  stages.value.push({ tables, value: createDefaultSelection() });
}

const stage_header_bg_colors = [
  'bg-purple-600',
  'bg-green-500',
  'bg-orange-500',
  'bg-fuchsia-600',
  'bg-teal-500',
];

function removeSelectionFromStage(stage) {
  const column_alias = stage.tables.map(table => table.columns.map(column => column.alias)).flat();

  // Filter selected fields
  let field_alias = stage.value.columns.map(field => field.alias);
  const is_includes = f => column_alias.includes(f) || field_alias.includes(f);

  if (stage.value.columns?.length) {
    const original = stage.value.columns.slice();
    const kept = [];

    const removeAlias = (alias) => {
      field_alias = field_alias.filter(f => f !== alias);
    };

    // 1) Plain fields (no agg, no expression)
    for (const field of original) {
      if (!field.agg && !field.expression) {
        const alias_without_aggregation = constructColumnAlias({ table_name: field.table_name, column_name: field.label });
        const is_aggregation_present_in_column = column_alias.includes(alias_without_aggregation); // covers cases where alias in columns may be aggregation form
        const keep = column_alias.includes(field.alias) || is_aggregation_present_in_column;
        if (keep)
          kept.push(field);
        else removeAlias(field.alias);
      }
    }

    // 2) Aggregation fields
    for (const field of original) {
      if (field.agg && !field.expression) {
        const alias_without_aggregation = constructColumnAlias({ table_name: field.table_name, column_name: field.label });
        const is_aggregation_present_in_column = column_alias.includes(alias_without_aggregation); // Only for the aggregations as the alias contains agg type
        const keep = column_alias.includes(field.alias) || is_aggregation_present_in_column;
        if (keep)
          kept.push(field);
        else removeAlias(field.alias);
      }
    }

    // 3) Expression fields
    for (const field of original) {
      if (field.expression) {
        const refs = field.fields.map(f => f.split(' > ').pop());
        const keep = refs.every(is_includes);
        if (keep)
          kept.push(field);
        else removeAlias(field.alias);
      }
    }

    // Preserve ordering by keeping fields in the order they were encountered above
    stage.value.columns = kept;
  }

  // Update the selection fields with the new filtered fields
  field_alias = stage.value.columns.map(field => field.alias);

  // Update orderBy fields
  if (stage.value.orderBy?.length)
    stage.value.orderBy = stage.value.orderBy.filter(field => field_alias.includes(field.alias));
}

function filterPreviousTables(tables, current_table) {
  const current_table_columns = current_table.columns.map(column => column.label);
  return tables.filter((table, index) => index !== 0 && current_table_columns.includes(table.on.left));
}

function syncPipelineFrom(index, should_remove_selection = false) {
  if (should_remove_selection) {
    removeSelectionFromStage(stages.value[index]);
  }
  for (let i = (index + 1); i < stages.value.length; i++) {
    const tables = getTablesFromStageFields(stages.value[i - 1]);
    const current_table = tables[0];
    const previous_tables = stages.value[i].tables;
    stages.value[i].tables = [current_table, ...filterPreviousTables(previous_tables, current_table)];
    if (should_remove_selection)
      removeSelectionFromStage(stages.value[i]);
  }
}

function onFieldsAdded(index) {
  syncPipelineFrom(index);
}

function onFieldsDeleted(index) {
  syncPipelineFrom(index, true);
  stages.value = stages.value.filter(stage => stage.tables[0].columns.length);
}
</script>

<template>
  <div class="w-full h-full">
    <div v-for="(stage, index) in stages" :key="index" class="mb-2 border-b pb-2 ">
      <div v-if="stages.length > 1" class="flex justify-between items-center">
        <div class="text-sm text-gray-700 font-semibold flex items-center p-2  mb-2 text-white rounded-sm" :class="stage_header_bg_colors[index % stage_header_bg_colors.length]">
          {{ $t('Stage') }} {{ index + 1 }}
        </div>
        <div class="gap-2 flex">
          <bi-query-builder-add-join :primary-dataset="stage.tables[0]" :selected-tables="stage.tables" :all-tables="all_tables" @save="stage.tables.push($event)" />
          <hawk-button icon type="text" size="xs" :disabled="index === 0" @click="stages.splice(index, 1)">
            <IconHawkTrashThree class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div v-if="selected_table && index === 0" class="text-sm text-gray-700 font-semibold flex items-center justify-between gap-1">
        <div class="flex items-center">
          <IconHawkDatabaseTwo class="size-4 mr-1" /> {{ selected_table.label }}
        </div>
        <div v-if="stages.length === 1">
          <bi-query-builder-add-join :primary-dataset="stage.tables[0]" :selected-tables="stage.tables" :all-tables="all_tables" @save="stage.tables.push($event)" />
        </div>
      </div>
      <div v-if="stage.tables.length > 1" class="pl-3">
        <div v-for="(table, table_index) in stage.tables" :key="table" class="pl-3  hover:bg-gray-50 rounded-md flex justify-between group">
          <div v-if="table_index !== 0" class="flex py-2 relative">
            <div class="border-l absolute -translate-y-[105%] border-gray-300 left-3" :class="{ 'h-3': table_index === 1, 'h-8': table_index > 1 }" />
            <div class="rounded-md p-1 w-6 h-6 flex items-center justify-center border">
              <component :is="join_types[table.type]" class=" text-gray-600" />
            </div>
            <div class="w-4 h-0 border-t border-1 border-gray-300 mt-3 mx-2" />
            <div>
              <div class="text-sm font-semibold text-gray-700">
                {{ table.label }}
              </div>
              <div class="text-xs font-medium text-gray-600 mt-2">
                {{ $t('on') }} {{ table.on.left }} {{ table.condition }} {{ table.on.right }}
              </div>
            </div>
          </div>
          <div v-if="table_index !== 0" class="p-2 hidden group-hover:block">
            <IconHawkXClose class="text-gray-500 hover:text-gray-700 cursor-pointer size-4" @click="stage.tables.splice(table_index, 1);removeSelectionFromStage(stage);syncPipelineFrom(index, true)" />
          </div>
        </div>
      </div>
      <bi-query-builder-stage v-model="stage.value" class="mt-2" :selected-table="stage.selected_table" :tables="stage.tables" :stage-index="index" @fields-deleted="onFieldsDeleted(index)" @fields-added="onFieldsAdded(index)" />
    </div>
    <div class="mt-2 pt-2 pb-4">
      <hawk-button type="light" block :disabled="stages[stages.length - 1]?.value?.columns?.filter(col => col?.agg || col?.expression)?.length === 0" @click="addStage">
        <IconHawkPlus /> {{ $t('Add stage') }}
      </hawk-button>
    </div>
  </div>
</template>
