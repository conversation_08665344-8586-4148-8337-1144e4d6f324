<script setup>
const emit = defineEmits(['close', 'save']);

const form$ = ref(null);

const state = reactive({
  is_loading: false,
});

function onSave() {
  state.is_loading = true;
  emit('save', form$.value.data.dashboard_name);
}
</script>

<template>
  <HawkModalContainer>
    <Vueform
      ref="form$"
      size="sm"
      class="max-w-[600px]"
      :columns="{
        default: { container: 12, label: 4, wrapper: 12 },
        sm: { container: 12, label: 4, wrapper: 12 },
        md: { container: 12, label: 4, wrapper: 12 },
      }"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              Create dashboard
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent>
          <TextElement
            name="dashboard_name"
            class="w-full"
            :label="$t('Name')"
            @keydown.enter="onRename()"
          />
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="emit('close')"
              >
                {{ $t('Cancel') }}
              </ButtonElement>
              <ButtonElement
                name="rename"
                :loading="state.is_loading"
                @click="onSave"
              >
                {{ $t('Save') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
