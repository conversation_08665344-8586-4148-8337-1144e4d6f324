<script setup>
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { BI_DEFAULT_PALETTE_COLORS } from '~/bi/constants/bi-constants';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['referenceLineConfigChange', 'markerConfigChange']);

const { markers_value_options } = useBiChartBuilderHelpers();

const reference_line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

const reference_line_value_options = computed(() => {
  return {
    min: 'Minimum',
    max: 'Maximum',
    average: 'Average',
    median: 'Median',
    p25: '25th percentile',
    p75: '75th percentile',
    p90: '90th percentile',
    p95: '95th percentile',
  };
});

function onReferenceLineItemChange(event, index) {
  emit('referenceLineConfigChange', event, index);
}

async function onAddReferenceLine(index) {
  await nextTick();
  emit('referenceLineConfigChange', {
    color: BI_DEFAULT_PALETTE_COLORS[Math.floor(Math.random() * BI_DEFAULT_PALETTE_COLORS.length)],
    label: `Reference line ${index + 1}`,
    value: null,
    series: props.chartConfig.layout_values[0]?.value,
    line_style: 'dashed',
  }, index);
}

function onMarkerItemChange(event, index) {
  emit('markerConfigChange', event, index);
}

async function onAddMarker(index) {
  await nextTick();
  emit('markerConfigChange', {
    color: BI_DEFAULT_PALETTE_COLORS[Math.floor(Math.random() * BI_DEFAULT_PALETTE_COLORS.length)],
    label: `Marker ${index + 1}`,
    value: null,
  }, index);
}
</script>

<template>
  <template v-if="['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType)">
    <ListElement
      name="reference_lines"
      label="Reference lines"
      add-text="+ Add reference line"
      :controls="{ add: true, remove: true, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
          remove: '!-right-4',
        },
      }"
      :remove-classes="{
        ListElement: {
          remove: 'left-0',
        },
      }"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
      :initial="0"
      @add="onAddReferenceLine"
    >
      <template #default="{ index }">
        <ObjectElement :name="index" class="border rounded px-3 pt-3 pb-1">
          <TextElement
            name="label"
            @change="onReferenceLineItemChange({ label: $event }, index)"
          >
            <template #addon-before>
              <BiColorPicker
                type="plain"
                :active-color="props.chartConfig?.reference_lines?.[index]?.color"
                @color-selected="onReferenceLineItemChange({ color: $event }, index)"
              />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <IconHawkXClose
                  class="w-4 h-4 cursor-pointer"
                  @click="props.formInstance.elements$.reference_lines.remove(index)"
                />
              </div>
            </template>
          </TextElement>
          <SelectElement
            name="series"
            label="Series"
            :items="props.chartConfig.layout_values"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            @change="onReferenceLineItemChange({ series: $event }, index)"
          />
          <SelectElement
            name="value"
            label="Value"
            :items="reference_line_value_options"
            :create="true"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            :append-new-option="false"
            @change="onReferenceLineItemChange({ value: $event }, index)"
          >
            <template #after>
              <div v-if="!['min', 'max', 'average', 'median', 'p25', 'p75', 'p90', 'p95'].includes(props.chartConfig?.reference_lines?.[index]?.value) && Number.isNaN(Number.parseInt(props.chartConfig?.reference_lines?.[index]?.value))" class="text-error-600 text-sm mt-1">
                Please enter a valid value
              </div>
            </template>
          </SelectElement>
          <div class="col-span-12">
            <label class="text-sm font-medium text-gray-700">Line style</label>
            <div class="mt-1">
              <HawkButtonGroup
                :items="reference_line_styles"
                icon
                size="sm"
                :active_item="props.chartConfig?.reference_lines?.[index]?.line_style"
                class="w-fit"
                @select="onReferenceLineItemChange({ line_style: $event.uid }, index)"
              />
            </div>
          </div>
          <HiddenElement name="color" class="h-0" />
          <HiddenElement name="line_style" class="h-0" />
        </ObjectElement>
      </template>
    </ListElement>
  </template>
  <template v-if="['gauge_chart', 'progress_chart'].includes(props.chartType)">
    <ListElement
      name="markers"
      label="Markers"
      add-text="+ Add marker"
      :controls="{ add: true, remove: true, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
          remove: '!-right-4',
        },
      }"
      :remove-classes="{
        ListElement: {
          remove: 'left-0',
        },
      }"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
      :initial="0"
      @add="onAddMarker"
    >
      <template #default="{ index }">
        <ObjectElement :name="index" class="border rounded px-3 pt-3 pb-1">
          <TextElement
            name="label"
            @change="onMarkerItemChange({ label: $event }, index)"
          >
            <template #addon-before>
              <BiColorPicker
                type="plain"
                :active-color="props.chartConfig?.markers?.[index]?.color"
                @color-selected="onMarkerItemChange({ color: $event }, index)"
              />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <IconHawkXClose
                  class="w-4 h-4 cursor-pointer"
                  @click="props.formInstance.elements$.markers.remove(index)"
                />
              </div>
            </template>
          </TextElement>
          <SelectElement
            name="value"
            label="Value"
            :items="markers_value_options"
            :create="true"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            :append-new-option="false"
            @change="onMarkerItemChange({ value: $event }, index)"
          >
            <template #after>
              <div v-if="!markers_value_options.includes(props.chartConfig?.markers?.[index]?.value) && Number.isNaN(Number.parseInt(props.chartConfig?.markers?.[index]?.value))" class="text-error-600 text-sm mt-1">
                Please enter a valid value
              </div>
            </template>
          </SelectElement>
          <HiddenElement name="color" class="h-0" />
        </ObjectElement>
      </template>
    </ListElement>
  </template>
</template>
