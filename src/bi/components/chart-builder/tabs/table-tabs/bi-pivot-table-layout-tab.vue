<script setup>
import { isEqual, pick } from 'lodash';
import { defineAsyncComponent, ref, watch } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const draggable = defineAsyncComponent(() => import('vuedraggable'));

const { getIconsForType } = useBIQueryBuilder();
const bi_store = useBiStore();

const columns = ref([]);
const rows = ref([]);
const values = ref([]);
const is_dragging = ref(false);
const current_dragged_element = ref(null);

function onDragEnd() {
  is_dragging.value = true;
  const has_values_changed
    = !isEqual(bi_store.widget_builder_config.chart.pivot_columns, columns.value)
    || !isEqual(bi_store.widget_builder_config.chart.pivot_rows, rows.value)
    || !isEqual(bi_store.widget_builder_config.chart.pivot_values, values.value);

  bi_store.widget_builder_config.chart.pivot_columns = columns.value;
  bi_store.widget_builder_config.chart.pivot_rows = rows.value;
  bi_store.widget_builder_config.chart.pivot_values = values.value;

  bi_store.is_table_dirty = bi_store.is_table_dirty || has_values_changed;
  is_dragging.value = false;
}

function onDragStart(evt) {
  current_dragged_element.value = evt.item.__draggable_context?.element || null;
}

function getDropGroup(target_zone) {
  return {
    name: 'fields',
    pull: true,
    put: () => {
      return allowDrop(current_dragged_element.value, target_zone);
    },
  };
}

function allowDrop(element, target_zone) {
  if (!element)
    return false;

  const type = getFieldIconType(element);
  if (target_zone === 'rows' || target_zone === 'columns') {
    return type !== 'function';
  }

  if (target_zone === 'values') {
    return ['integer', 'numeric'].includes(type) || type === 'function';
  }

  return true;
}

function updateTableConfig(key, value) {
  bi_store.widget_builder_config.chart[key] = value;
  bi_store.is_table_dirty = true;
}

function getFieldIconType(alias) {
  const field = bi_store.alias_to_column_mapping()[alias];
  return field?.is_aggregation ? 'function' : field?.type;
}

watch(() => pick(bi_store.widget_builder_config.chart, ['pivot_columns', 'pivot_rows', 'pivot_values']), () => {
  if (is_dragging.value)
    return;
  columns.value = bi_store.widget_builder_config.chart?.pivot_columns || [];
  rows.value = bi_store.widget_builder_config.chart?.pivot_rows || [];
  values.value = bi_store.widget_builder_config.chart?.pivot_values || [];
}, { immediate: true, deep: true });
</script>

<template>
  <div class="space-y-6">
    <!-- Columns Section -->
    <div class="">
      <div class="font-semibold mb-3 text-sm">
        {{ $t("Columns") }}
      </div>
      <draggable
        v-model="columns"
        :group="getDropGroup('columns')"
        item-key="key"
        handle=".move"
        class="min-h-[50px] gap-y-1"
        @start="onDragStart"
        @end="onDragEnd"
      >
        <template #item="{ element }">
          <div class="p-2 rounded-md group hover:bg-gray-50">
            <div class="flex items-center">
              <div class="pr-3 move cursor-move">
                <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
              </div>
              <div class="flex items-center">
                <div>
                  <component :is="getIconsForType(getFieldIconType(element))" class="text-gray-600 w-4 h-4" />
                </div>
                <span class="text-sm text-gray-600 pl-2">{{ element }}</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- Rows Section -->
    <div class="">
      <div class="font-semibold mb-3 text-sm">
        {{ $t('Rows') }}
      </div>
      <draggable
        v-model="rows"
        :group="getDropGroup('rows')"
        item-key="key"
        handle=".move"
        class="min-h-[50px] gap-y-1"
        @start="onDragStart"
        @end="onDragEnd"
      >
        <template #item="{ element }">
          <div class="p-2 rounded-md group hover:bg-gray-50">
            <div class="flex items-center">
              <div class="pr-2 move cursor-move">
                <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
              </div>
              <div class="flex items-center">
                <div>
                  <component :is="getIconsForType(getFieldIconType(element))" class="text-gray-600 w-4 h-4" />
                </div>
                <span class="text-sm text-gray-600 pl-2">{{ element }}</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <div class="border-b border-gray-200 mb-6" />

    <!-- Values Section (internally draggable) -->
    <div class="">
      <div class="font-semibold mb-3 text-sm">
        {{ $t('Values') }}
      </div>
      <draggable
        v-model="values"
        :group="getDropGroup('values')"
        item-key="key"
        handle=".move"
        class="min-h-[50px] gap-y-1"
        @start="onDragStart"
        @end="onDragEnd"
      >
        <template #item="{ element }">
          <div class="p-2 rounded-md group hover:bg-gray-50">
            <div class="flex items-center">
              <div class="pr-2 move cursor-move">
                <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
              </div>
              <div class="flex items-center">
                <div :class="{ 'border-l-4 border-warning-300': getFieldIconType(element) === 'function' }">
                  <component :is="getIconsForType(getFieldIconType(element))" class="text-gray-600 w-4 h-4" />
                </div>
                <span class="text-sm text-gray-600 pl-2">{{ element }}</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <div class="border-b border-gray-200 mb-3" />

    <Vueform size="sm" class="mt-3">
      <div class="col-span-12">
        <div class="flex items-start justify-between mb-4">
          <div>
            <div class="text-sm font-medium text-gray-700">
              {{ $t('Show column totals') }}
            </div>
          </div>
          <ToggleElement
            name="show_column_totals"
            class="ml-2"
            :default="bi_store.widget_builder_config.chart.show_column_totals"
            @change="updateTableConfig('show_column_totals', $event)"
          />
        </div>
        <div class="flex items-start justify-between mb-4">
          <div>
            <div class="text-sm font-medium text-gray-700">
              {{ $t('Show row totals') }}
            </div>
          </div>
          <ToggleElement
            name="show_row_totals"
            class="ml-2"
            :default="bi_store.widget_builder_config.chart.show_row_totals"
            @change="updateTableConfig('show_row_totals', $event)"
          />
        </div>
        <div class="flex items-start justify-between mb-4">
          <div>
            <div class="text-sm font-medium text-gray-700">
              {{ $t('Show grand totals') }}
            </div>
          </div>
          <ToggleElement
            name="show_row_totals"
            class="ml-2"
            :default="bi_store.widget_builder_config.chart.show_row_totals"
            @change="updateTableConfig('show_grand_totals', $event)"
          />
        </div>
      </div>
    </Vueform>
  </div>
</template>
