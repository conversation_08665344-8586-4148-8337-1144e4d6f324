<script setup>
import chroma from 'chroma-js';
import { defineAsyncComponent, onMounted, reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  rules: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['updateRules']);

const draggable = defineAsyncComponent(() => import('vuedraggable'));

const bi_store = useBiStore();

const state = reactive({
  show_rules_form: false,
  current_rule_uid: null,
  rules: [],
});

const fields = computed(() => {
  if (props.chartType === 'pivot_table')
    return bi_store.widget_builder_config.chart.pivot_values || [];
  if (props.chartType === 'table')
    return Object.keys(bi_store.widget_builder_config.chart.columns_map || {});
  return [];
});

function getRuleColors(rule) {
  const data = chroma.scale(['#ffffff', rule.color]).mode('lch').colors(rule.color_range);
  return data;
}

function onDragEnd() {
  emits('updateRules', state.rules);
}

function showRuleForm(rule = null) {
  state.current_rule_uid = rule?.uid;
  state.show_rules_form = true;
}

function onRuleChange(rule) {
  const index = state.rules.findIndex(r => r.uid === rule.uid);
  if (index === -1)
    state.rules.push(rule);
  else
    state.rules[index] = rule;

  state.current_rule_uid = null;
  state.show_rules_form = false;

  emits('updateRules', state.rules);
}

function onRuleFormClose() {
  state.current_rule_uid = null;
  state.show_rules_form = false;
}

onMounted(() => {
  state.rules = props.rules;
});
</script>

<template>
  <div class="space-y-4">
    <BiRuleFieldForm
      v-if="state.show_rules_form"
      :fields="fields"
      :fields-map="bi_store.alias_to_column_mapping()"
      :rule-details="state.rules.find(r => r.uid === state.current_rule_uid)"
      @rule-change="onRuleChange"
      @close="onRuleFormClose()"
    />
    <!-- Listing Rules -->
    <div v-else>
      <div class="text-sm font-semibold text-gray-900 mb-4">
        {{ $t('Rules') }}
      </div>

      <div v-if="!state.rules.length" class="text-sm text-gray-600 mb-4">
        No rules added yet
      </div>

      <draggable
        v-model="state.rules"
        :group="{ name: 'rules' }"
        item-key="id"
        handle=".drag-handle"
        class="space-y-3"
        @end="onDragEnd"
      >
        <template #item="{ element: rule }">
          <div class="rounded-lg p-2 border border-gray-200 group hover:bg-gray-50 cursor-pointer">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2 flex-1">
                <IconHawkDragIcon class="w-3 h-3 text-gray-400 cursor-move drag-handle" />

                <div class="cursor-pointer" @click="showRuleForm(rule)">
                  <div v-if="rule.formatting_style === 'single_color'" class="text-sm flex items-center gap-x-1.5">
                    <span class="text-gray-500">When</span>
                    <span class="text-gray-900">
                      "{{ rule.field }}"
                      {{ rule.operator }}
                      {{ rule.value }}
                    </span>
                  </div>
                  <div v-if="rule.formatting_style === 'color_range'" class="text-sm flex items-center gap-x-1.5">
                    <span class="text-gray-900">Applied for {{ rule.field }}</span>
                  </div>
                </div>
              </div>

              <!-- Single Color Display -->
              <div v-if="rule.formatting_style === 'single_color'" class="flex items-center gap-2">
                <div
                  class="w-4 h-4 rounded-full"
                  :style="{ backgroundColor: rule.color }"
                />
              </div>

              <!-- Color Range Display -->
              <div v-else-if="rule.formatting_style === 'color_range'" class="flex items-center gap-2">
                <div class="flex rounded-md overflow-hidden border border-gray-200">
                  <div
                    v-for="color in getRuleColors(rule)"
                    :key="color"
                    class="h-5"
                    :style="{
                      backgroundColor: color,
                      width: `${Math.max(12, Math.min(32, Math.floor(96 / getRuleColors(rule).length)))}px`,
                    }"
                  />
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>

      <div class="border-b border-gray-200 mb-6" />

      <HawkButton type="link" @click="showRuleForm()">
        <IconHawkPlus
          class="text-primary-700 w-5 h-4.75 mx-1"
        />
        <div class="font-semibold">
          {{ $t('Add rule') }}
        </div>
      </HawkButton>
    </div>
  </div>
</template>
