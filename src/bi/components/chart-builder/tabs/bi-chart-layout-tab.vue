<script setup>
import { storeToRefs } from 'pinia';
import { BI_DEFAULT_PALETTE_COLORS, CHART_TO_CATEGORY_TYPE_MAP, CHART_TO_VALUE_TYPE_MAP } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['seriesConfigChange']);

const bi_store = useBiStore();
const { widget_data } = storeToRefs(bi_store);
const { chart_builder_data_types } = storeToRefs(bi_store);

const all_columns = computed(() => {
  return Object.keys(widget_data.value[0]);
});

const category_columns = computed(() => {
  return all_columns.value.filter((column) => {
    if (CHART_TO_CATEGORY_TYPE_MAP[props.chartType].includes(chart_builder_data_types.value[column]))
      return true;
    return false;
  });
});

const stack_by_columns = computed(() => {
  return all_columns.value.filter((column) => {
    return chart_builder_data_types.value[column] === 'text';
  });
});

const default_series_values = computed(() => {
  let type = props.chartType.replace('_chart', '');
  if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
    type = 'bar';
  return [
    {
      value: getValueColumns()[0],
      label: getValueColumns()[0],
      type,
      color: BI_DEFAULT_PALETTE_COLORS[0],
      y_axis: 'primary',
      line_style: 'solid',
      line_width: 1,
      line_shape: 'straight',
      prefix: '',
      suffix: '',
      stack: props.chartConfig.stack_by !== 'none',
    },
  ];
});

function getValueColumns(index = null) {
  return all_columns.value.filter((column) => {
    if (index && props.chartConfig?.layout_values?.some?.((item, item_index) => item.label === column && item_index !== index))
      return false;
    if (CHART_TO_VALUE_TYPE_MAP[props.chartType].includes(chart_builder_data_types.value[column]))
      return true;
    return false;
  });
}

function onSeriesItemChange(payload, index) {
  emit('seriesConfigChange', payload, index);
}

async function onAddSeries(index) {
  await nextTick();
  let type = props.chartType.replace('_chart', '');
  if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
    type = 'bar';
  emit(
    'seriesConfigChange',
    // Defaults
    {
      type,
      color: BI_DEFAULT_PALETTE_COLORS[index % BI_DEFAULT_PALETTE_COLORS.length],
      y_axis: 'primary',
      line_style: 'solid',
      line_width: 1,
      line_shape: 'straight',
      prefix: '',
      suffix: '',
      stack: props.chartConfig.stack_by !== 'none',
    },
    index,
  );
}

function getAxis(type) {
  // type = 'category' || 'value'
  if (props.chartConfig.type === 'horizontalBar_chart') {
    if (type === 'category')
      return 'y';
    else return 'x';
  }
  else {
    if (type === 'category')
      return 'x';
    else return 'y';
  }
}
</script>

<template>
  <template v-if="['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
      rules="required"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ListElement
      name="layout_values"
      rules="required"
      :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
      :sort="true"
      :controls="{ add: true, remove: false, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
        },
      }"
      add-text="+ Add another series"
      :min="1"
      :initial="1"
      :default="default_series_values"
      @add="onAddSeries"
    >
      <template #default="{ index }">
        <ObjectElement
          :name="index"
        >
          <SelectElement
            v-if="!props.chartConfig?.layout_values?.[index]?.label"
            name="value"
            rules="required"
            :native="false"
            :items="getValueColumns(index)"
            :add-classes="{
              SelectElement: {
                select: {
                  wrapper: 'ml-4',
                },
              },
            }"
            @change="onSeriesItemChange({ label: $event }, index)"
          >
            <template #caret>
              <IconHawkXClose
                class="w-4 h-4 cursor-pointer ml-1 mr-2"
                @click="props.formInstance.elements$.layout_values.remove(index)"
              />
            </template>
          </SelectElement>
          <!-- NOTE: The legend field is derived from label. Therefore, while adding a field, we use label and while saving we use legend -->
          <TextElement
            v-else
            name="legend"
            :default="props.chartConfig?.layout_values?.[index]?.label"
            :add-classes="{
              TextElement: {
                inputContainer: 'pl-6',
              },
            }"
          >
            <template #addon-before>
              <BiColorPicker
                type="plain"
                :active-color="props.chartConfig?.layout_values?.[index].color"
                @color-selected="onSeriesItemChange({ color: $event }, index)"
              />
            </template>
            <template #addon-after>
              <BiSeriesContextMenu
                :chart-config="props.chartConfig"
                :series-config="props.chartConfig?.layout_values?.[index]"
                :columns="getValueColumns(index)"
                @field-selected="onSeriesItemChange($event, index)"
              />
              <IconHawkXClose
                v-if="props.chartConfig?.layout_values?.length > 1"
                class="w-4 h-4 cursor-pointer ml-1"
                @click="props.formInstance.elements$.layout_values.remove(index)"
              />
            </template>
          </TextElement>
          <HiddenElement name="type" />
          <HiddenElement name="y_axis" />
          <HiddenElement name="line_style" />
          <HiddenElement name="line_width" />
          <HiddenElement name="line_shape" />
          <HiddenElement name="prefix" />
          <HiddenElement name="suffix" />
          <HiddenElement name="stack" />
        </ObjectElement>
      </template>
    </ListElement>
    <SelectElement
      v-if="!['scatter_chart'].includes(props.chartType)"
      name="stack_by"
      label="Stack by"
      default="none"
      :items="[
        { value: 'none', label: 'None' },
        ...stack_by_columns,
      ]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else-if="['pie_chart', 'donut_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      rules="required"
      label="Category"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="layout_values"
      rules="required"
      label="Value"
      :items="getValueColumns()"
      :default="getValueColumns()[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else-if="['heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      rules="required"
      label="X-axis"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="layout_values"
      rules="required"
      label="Y-axis"
      :items="getValueColumns()"
      :default="getValueColumns()[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <template v-if="['heatmap_chart'].includes(props.chartType)">
      <SelectElement
        name="stack_by"
        rules="required"
        label="Stack by"
        :items="[
          ...stack_by_columns,
        ]"
        :default="stack_by_columns[0]"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
      />
    </template>
  </template>
  <template v-else-if="['gauge_chart', 'progress_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_values"
      rules="required"
      label="Value"
      :items="getValueColumns()"
      :default="getValueColumns()[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <BiPivotTableLayoutTab
    v-else-if="props.chartType === 'pivot_table'"
  />
  <BiTableLayoutTab
    v-else-if="props.chartType === 'table'"
  />
  <template v-else>
    Layout - {{ props.chartType }}
  </template>
</template>
