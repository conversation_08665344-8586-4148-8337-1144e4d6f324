<script setup>
import { storeToRefs } from 'pinia';
import { nextTick } from 'vue';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { BI_DEFAULT_PALETTE_COLORS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['colorRangeConfigChange']);

const { markers_value_options } = useBiChartBuilderHelpers();

const bi_store = useBiStore();
const { chart_builder_data_types } = storeToRefs(bi_store);

const show_legend = computed(() => !['waterfall_chart'].includes(props.chartType));

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

const state = reactive({
  eighty_percent_line_color: null,
  eighty_percent_line_style: null,
  cumulative_line_color: null,
  bar_color: null,
  positive_color: null,
  negative_color: null,
});

function onFieldSelected(field, value) {
  state[field] = value;
  props.formInstance.update({
    [field]: value,
  });
}

function onColorRangeItemChange(event, index) {
  emit('colorRangeConfigChange', event, index);
}

async function onAddColorRange(index) {
  await nextTick();
  emit('colorRangeConfigChange', {
    color: BI_DEFAULT_PALETTE_COLORS[Math.floor(Math.random() * BI_DEFAULT_PALETTE_COLORS.length)],
    label: `Range ${index + 1}`,
    value: null,
  }, index);
}

watch(() => props.chartConfig, () => {
  ['eighty_percent_line_color', 'eighty_percent_line_style', 'cumulative_line_color', 'bar_color', 'positive_color', 'negative_color'].forEach((key) => {
    state[key] = props.chartConfig[key];
  });
});
</script>

<template>
  <TextElement
    name="title"
    :label="$t('Title')"
    :placeholder="$t('Enter title')"
    description="Main chart title displayed at the top"
  />
  <TextElement
    name="subtitle"
    :label="$t('Subtitle')"
    :placeholder="$t('Enter subtitle')"
    description="Secondary title below the main title"
  />
  <SelectElement
    v-if="show_legend"
    name="legend"
    label="Legend"
    :items="{
      bottom: $t('Bottom'),
      right: $t('Right'),
      top: $t('Top'),
      left: $t('Left'),
      hide: $t('Hide'),
    }"
    default="bottom"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <ToggleElement
    v-if="['column_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(props.chartType) && props.chartConfig.layout_category && chart_builder_data_types[props.chartConfig.layout_category] === 'date'"
    name="timeseries"
    label="Timeseries"
  />
  <SelectElement
    name="values"
    :label="$t('Values')"
    :items="{
      show: $t('Show'),
      hide: $t('Hide'),
    }"
    default="show"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <SliderElement
    name="precision"
    label="Precision"
    :min="0"
    :max="10"
    :default="2"
    :step="1"
    :conditions="[['values', 'show']]"
  />
  <ToggleElement
    name="compact"
    label="Compact"
    description="Show values in compact format"
    :conditions="[['values', 'show']]"
  />
  <template v-if="['waterfall_chart'].includes(props.chartType)">
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Positive color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.positive_color"
          @color-selected="onFieldSelected('positive_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Negative color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.negative_color"
          @color-selected="onFieldSelected('negative_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Show totals
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="show_sum"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <HiddenElement name="positive_color" />
    <HiddenElement name="negative_color" />
  </template>
  <template v-if="['pareto_chart'].includes(props.chartType)">
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Bar color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.bar_color"
          @color-selected="onFieldSelected('bar_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Cumulative line color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.cumulative_line_color"
          @color-selected="onFieldSelected('cumulative_line_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        80% line
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="show_eighty_percent_line"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_eighty_percent_line" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        &mdash; color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.eighty_percent_line_color"
          @color-selected="onFieldSelected('eighty_percent_line_color', $event)"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_eighty_percent_line" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        &mdash; style
      </div>
      <div class="col-span-8">
        <HawkButtonGroup
          :items="line_styles"
          icon
          size="sm"
          :active_item="state.eighty_percent_line_style"
          class="w-fit"
          @select="onFieldSelected('eighty_percent_line_style', $event.uid)"
        />
      </div>
    </div>
    <HiddenElement name="eighty_percent_line_color" />
    <HiddenElement name="eighty_percent_line_style" />
    <HiddenElement name="cumulative_line_color" />
    <HiddenElement name="bar_color" />
  </template>
  <template v-if="['pyramid_chart', 'funnel_chart'].includes(props.chartType)">
    <ToggleElement
      name="show_labels_at_center"
      label="Show labels at center"
    />
    <ToggleElement
      name="show_percentages"
      label="Show percentages"
      :conditions="[['values', '==', 'show']]"
    />
    <ToggleElement
      v-if="['funnel_chart'].includes(props.chartType)"
      name="compare_with_previous"
      label="Compare with previous"
    />
    <ToggleElement
      v-if="['funnel_chart'].includes(props.chartType)"
      name="maintain_slope"
      label="Maintain slope"
    />
    <ToggleElement
      name="render_in_3d"
      label="Render in 3D"
    />
  </template>
  <template v-if="['heatmap_chart'].includes(props.chartType)">
    <SelectElement
      name="color_type"
      label="Color scheme"
      :items="{
        piecewise: 'Piecewise',
        continuous: 'Gradient',
      }"
      default="continuous"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <BiColorPalettePicker
      v-if="props.chartConfig.color_type === 'continuous'"
      name="color_scheme"
      variant="heatmap"
      :options="{ label: 'Gradient' }"
    />
  </template>
  <template v-if="['gauge_chart', 'progress_chart'].includes(props.chartType) || (['heatmap_chart'].includes(props.chartType) && props.chartConfig.color_type === 'piecewise')">
    <ListElement
      name="color_ranges"
      label="Color ranges"
      add-text="+ Add range"
      :controls="{ add: true, remove: true, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
          remove: '!-right-4',
        },
      }"
      :remove-classes="{
        ListElement: {
          remove: 'left-0',
        },
      }"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
      :initial="0"
      @add="onAddColorRange"
    >
      <template #default="{ index }">
        <ObjectElement :name="index" class="border rounded px-3 pt-3 pb-1">
          <TextElement
            v-if="props.chartType === 'heatmap_chart'"
            name="label"
            @change="onColorRangeItemChange({ label: $event }, index)"
          >
            <template #addon-before>
              <BiColorPicker
                type="plain"
                :active-color="props.chartConfig?.color_ranges?.[index]?.color"
                @color-selected="onColorRangeItemChange({ color: $event }, index)"
              />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <IconHawkXClose
                  class="w-4 h-4 cursor-pointer"
                  @click="props.formInstance.elements$.color_ranges.remove(index)"
                />
              </div>
            </template>
          </TextElement>
          <div v-else class="col-span-12 flex gap-6">
            <div class="text-sm font-medium text-gray-700">
              Range color
            </div>
            <div>
              <BiColorPicker
                type="outlined"
                :active-color="props.chartConfig?.color_ranges?.[index]?.color"
                @color-selected="onColorRangeItemChange({ color: $event }, index)"
              />
            </div>
          </div>
          <div class="col-span-12 flex gap-3">
            <SelectElement
              name="min"
              label="Min"
              :items="markers_value_options"
              :create="true"
              :native="false"
              :can-clear="false"
              :can-deselect="false"
              :append-new-option="false"
              class="w-full"
              @change="onColorRangeItemChange({ min: $event }, index)"
            >
              <template #after>
                <div v-if="!markers_value_options.includes(props.chartConfig?.color_ranges?.[index]?.min) && Number.isNaN(Number.parseInt(props.chartConfig?.color_ranges?.[index]?.min))" class="text-error-600 text-sm mt-1">
                  Please enter a valid value
                </div>
              </template>
            </SelectElement>
            <SelectElement
              name="max"
              label="Max"
              :items="markers_value_options"
              :create="true"
              :native="false"
              :can-clear="false"
              :can-deselect="false"
              :append-new-option="false"
              class="w-full"
              @change="onColorRangeItemChange({ max: $event }, index)"
            >
              <template #after>
                <div v-if="!markers_value_options.includes(props.chartConfig?.color_ranges?.[index]?.max) && Number.isNaN(Number.parseInt(props.chartConfig?.color_ranges?.[index]?.max))" class="text-error-600 text-sm mt-1">
                  Please enter a valid value
                </div>
              </template>
            </SelectElement>
          </div>
          <HiddenElement name="color" class="h-0" />
        </ObjectElement>
      </template>
    </ListElement>
  </template>
</template>
