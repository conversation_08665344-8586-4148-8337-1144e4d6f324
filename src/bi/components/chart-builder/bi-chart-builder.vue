<script setup>
import { isEqual } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { computed, nextTick, onMounted } from 'vue';
import { BI_CHART_BUILDER_TABS, BI_DEFAULT_PALETTE_COLORS, CHART_TO_CATEGORY_TYPE_MAP } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const emit = defineEmits(['goBack']);

const bi_store = useBiStore();
const { widget_builder_config, are_chart_builder_fields_filled, chart_builder_data_types } = storeToRefs(bi_store);
const { set_chart_builder_data_types } = bi_store;

const type_form$ = ref(null);
const form$ = ref(null);

const types = [
  ['pivot_table', 'Pivot table', IconHawkTableTwo],
  ['table', 'Table', IconHawkTableTwo],
  ['column_chart', 'Column chart', IconHawkHorizontalBarChartOne],
  ['horizontalBar_chart', 'Bar chart', IconHawkHorizontalBarChartOne],
  ['line_chart', 'Line chart', IconHawkLineChartUpOne],
  ['area_chart', 'Area chart', IconHawkAreaChart],
  ['mixed_chart', 'Mixed chart', IconHawkMixChart],
  ['pie_chart', 'Pie chart', IconHawkPieChartThree],
  ['donut_chart', 'Doughnut chart', IconHawkDoughnutChart],
  ['scatter_chart', 'Scatter chart', IconHawkScatterChart],
  ['gauge_chart', 'Gauge chart', IconHawkGaugeChart],
  ['progress_chart', 'Progress chart', IconHawkProgressChart],
  ['heatmap_chart', 'Heatmap chart', IconHawkHeatmapChart],
  ['pyramid_chart', 'Pyramid chart', IconHawkPyramidChart],
  ['funnel_chart', 'Funnel chart', IconHawkFunnelChart],
  ['pareto_chart', 'Pareto chart', IconHawkParetoChart],
  ['waterfall_chart', 'Waterfall chart', IconHawkWaterfallChart],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
    icon: item[2],
  };
});

const state = reactive({
  form_data: {},
  active_item: 'layout',
});

const tabs = computed(() => {
  const current_chart_tabs = BI_CHART_BUILDER_TABS[widget_builder_config.value.chart.type] || [];
  return [
    ...(current_chart_tabs.includes('layout') ? [{ uid: 'layout', label: 'Layout' }] : []),
    ...(current_chart_tabs.includes('display') ? [{ uid: 'display', label: 'Display' }] : []),
    ...(current_chart_tabs.includes('axes') ? [{ uid: 'axes', label: 'Axes' }] : []),
    ...(current_chart_tabs.includes('advanced') ? [{ uid: 'advanced', label: 'Advanced' }] : []),
    ...(current_chart_tabs.includes('conditional_formatting') ? [{ uid: 'conditional_formatting', label: 'Conditional formatting' }] : []),
  ];
});

const rules = computed(() => {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type)) {
    return bi_store.widget_builder_config.chart.conditional_formatting;
  }
  return [];
});
function onSeriesConfigChange(fields, index) {
  if (state.form_data?.layout_values?.[index]) {
    let has_type_changed = false;
    Object.keys(fields).forEach((field_name) => {
      if (field_name === 'type' && state.form_data.layout_values[index].type && state.form_data.layout_values[index].type !== fields.type)
        has_type_changed = true;
      state.form_data.layout_values[index][field_name] = fields[field_name];
    });
    if (has_type_changed)
      type_form$.value.update({ type: 'mixed_chart' });
  }
}

function onColorRangeConfigChange(fields, index) {
  if (state.form_data?.color_ranges?.[index]) {
    Object.keys(fields).forEach((field_name) => {
      state.form_data.color_ranges[index][field_name] = fields[field_name];
    });
  }
}

function onReferenceLineConfigChange(fields, index) {
  if (state.form_data?.reference_lines?.[index]) {
    Object.keys(fields).forEach((field_name) => {
      state.form_data.reference_lines[index][field_name] = fields[field_name];
    });
  }
}

function onMarkerConfigChange(fields, index) {
  if (state.form_data?.markers?.[index]) {
    Object.keys(fields).forEach((field_name) => {
      state.form_data.markers[index][field_name] = fields[field_name];
    });
  }
}

async function onChartTypeChange(new_type, old_type) {
  if (['table', 'pivot_table'].includes(old_type) || ['table', 'pivot_table'].includes(new_type)) {
    widget_builder_config.value.chart.conditional_formatting = [];
    widget_builder_config.value.chart = {
      ...widget_builder_config.value.chart,
      type: new_type,
    };
    state.active_item = 'layout';
    return;
  }
  const types_with_series = ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'];
  const is_chart_incompatible_with_the_previous = (types_with_series.includes(old_type) && !types_with_series.includes(new_type)) || (types_with_series.includes(new_type) && !types_with_series.includes(old_type));
  if (is_chart_incompatible_with_the_previous) {
    const updates = {};
    if (types_with_series.includes(new_type)) {
      let type = new_type.replace('_chart', '');
      if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
        type = 'bar';
      updates.layout_values = [{
        value: state.form_data.layout_values,
        label: state.form_data.layout_values,
        type,
        color: BI_DEFAULT_PALETTE_COLORS[0],
        y_axis: 'primary',
        line_style: 'solid',
        line_width: 1,
        line_shape: 'straight',
        prefix: '',
        suffix: '',
        stack: widget_builder_config.value.chart.stack_by && widget_builder_config.value.chart.stack_by !== 'none',
      }];
    }
    else {
      updates.layout_values = state.form_data.layout_values[0].value;
    }

    // ?. because a few charts like gauge and progress don't have category fields
    if (!CHART_TO_CATEGORY_TYPE_MAP[new_type]?.includes?.(chart_builder_data_types.value[state.form_data.layout_category]))
      updates.layout_category = null;
    form$.value.update(updates);
  }

  widget_builder_config.value.chart = {
    ...widget_builder_config.value.chart,
    type: new_type,
  };
  state.active_item = 'layout';

  // Switching to column, horizontalBar, line and area charts from themselves or mixed/scatter
  if (!is_chart_incompatible_with_the_previous && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart'].includes(new_type)) {
    await nextTick();
    form$.value.update({
      layout_values: state.form_data.layout_values.map((item) => {
        let type = new_type.replace('_chart', '');
        if (type === 'column' || type === 'horizontalBar')
          type = 'bar';
        return { ...item, type };
      }),
    });
  }
  // Switching between scatter and its closest series charts (since scatter has numeric category field, it is surely incompatible)
  if ((['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(old_type) && new_type === 'scatter_chart')
    || (old_type === 'scatter_chart' && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(new_type))) {
    await nextTick();
    form$.value.update({
      layout_category: null,
    });
  }
  if (new_type === 'pareto_chart') {
    await nextTick();
    form$.value.update({
      bar_color: BI_DEFAULT_PALETTE_COLORS[0],
      cumulative_line_color: BI_DEFAULT_PALETTE_COLORS[2],
      show_eighty_percent_line: false,
      eighty_percent_line_color: BI_DEFAULT_PALETTE_COLORS[1],
      eighty_percent_line_style: 'dashed',
    });
  }
  else if (new_type === 'waterfall_chart') {
    await nextTick();
    form$.value.update({
      show_sum: false,
      positive_color: BI_DEFAULT_PALETTE_COLORS[1],
      negative_color: BI_DEFAULT_PALETTE_COLORS[2],
    });
  }
  await validateForm();
}

async function validateForm() {
  setTimeout(async () => {
    await form$.value?.validate?.();
    if (form$.value?.invalid === false)
      are_chart_builder_fields_filled.value = true;
    else
      are_chart_builder_fields_filled.value = false;
  }, 0);
}

async function onVueformChange(new_config, old_config) {
  if (!isEqual(new_config.stack_by, old_config.stack_by) && new_config.stack_by && widget_builder_config.value.chart.type !== 'heatmap_chart') {
    form$.value.update({
      layout_values: state.form_data.layout_values.map(item => ({ ...item, stack: true })),
    });
  }
}

function onUpdateRules(rules) {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type)) {
    bi_store.widget_builder_config.chart.conditional_formatting = rules;
    bi_store.is_table_dirty = true;
  }
}

watch(() => state.form_data, async (new_config) => {
  await validateForm();
  widget_builder_config.value.chart = {
    ...widget_builder_config.value.chart,
    ...new_config,
  };
}, { deep: true });

onMounted(async () => {
  set_chart_builder_data_types();
  if (!widget_builder_config.value.chart.type) {
    widget_builder_config.value.chart = {
      type: 'table',
    };
  }
  else {
    state.form_data = {
      ...widget_builder_config.value.chart,
    };
    type_form$.value.load({ type: widget_builder_config.value.chart.type });
  }
});
</script>

<template>
  <div>
    <div
      class="flex items-center gap-2 cursor-pointer hover:underline text-sm font-semibold text-gray-700"
      @click="emit('goBack')"
    >
      <IconHawkArrowLeft />
      Back to data builder
    </div>

    <Vueform
      ref="type_form$"
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      class="mt-6"
    >
      <SelectElement
        name="type"
        default="table"
        :label="$t('Chart type')"
        :items="types"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
        :search="true"
        @change="onChartTypeChange"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <component :is="option.icon" class="text-gray-500" :class="{ '-rotate-90': option.value === 'column_chart' }" />
            {{ option.label }}
          </div>
        </template>
        <template #single-label="{ value }">
          <div class="w-full flex items-center gap-2 px-2">
            <component :is="value.icon" class="text-gray-500" :class="{ '-rotate-90': value.value === 'column_chart' }" />
            {{ value.label }}
          </div>
        </template>
      </SelectElement>
    </Vueform>
    <HawkTabs v-if="widget_builder_config.chart.type" :tabs="tabs" :active_item="state.active_item" class="col-span-12 mt-6" @tab-click="state.active_item = $event.uid" />
    <Vueform
      :key="widget_builder_config.chart.type"
      ref="form$"
      v-model="state.form_data"
      sync
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      :display-errors="false"
      :display-messages="false"
      :messages="{ required: $t('This field is required') }"
      class="mt-3"
      @change="onVueformChange"
    >
      <div v-show="state.active_item === 'layout'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartLayoutTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
          :form-instance="form$"
          @series-config-change="onSeriesConfigChange"
        />
      </div>
      <div v-show="state.active_item === 'display'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartDisplayTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
          :form-instance="form$"
          @color-range-config-change="onColorRangeConfigChange"
        />
      </div>
      <div v-show="state.active_item === 'axes'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAxesTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
        />
      </div>
      <div v-show="state.active_item === 'advanced'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAdvancedTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
          :form-instance="form$"
          @reference-line-config-change="onReferenceLineConfigChange"
          @marker-config-change="onMarkerConfigChange"
        />
      </div>
      <div v-if="state.active_item === 'conditional_formatting'" class="col-span-12 flex flex-col gap-y-5">
        <BiConditionalFormattingTab
          :chart-type="widget_builder_config.chart.type"
          :rules="rules"
          @update-rules="onUpdateRules"
        />
      </div>
    </Vueform>
  </div>
</template>
