<script setup>
const emit = defineEmits(['close']);
</script>

<template>
  <HawkModalContainer content_class="h-full w-full rounded-none">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <slot name="title" />
        </template>
      </HawkModalHeader>
      <HawkModalContent class="!p-0 max-h-[calc(100vh-85px)]">
        <div class="flex">
          <div class="w-1/4 py-4 px-4 h-[calc(100vh-85px)] border-r scrollbar">
            <slot name="left-content" />
          </div>
          <div class="h-[calc(100vh-85px)] w-3/4">
            <slot name="right-content" />
          </div>
        </div>
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
