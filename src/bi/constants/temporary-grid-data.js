const stages_one = [
  {
    selected_table: {
      label: 'progress_data',
      columns: [
        {
          name: 'ID',
          type: 'text',
          pgType: 'text',
          label: 'ID',
          alias: 'ID',
        },
        {
          name: 'Activity',
          type: 'text',
          pgType: 'text',
          label: 'Activity',
          alias: 'Activity',
        },
        {
          name: 'Subactivity',
          type: 'text',
          pgType: 'text',
          label: 'Subactivity',
          alias: 'Subactivity',
        },
        {
          name: 'Layer',
          type: 'text',
          pgType: 'text',
          label: 'Layer',
          alias: 'Layer',
        },
        {
          name: 'Sublayer',
          type: 'text',
          pgType: 'text',
          label: 'Sublayer',
          alias: 'Sublayer',
        },
        {
          name: 'UOM',
          type: 'text',
          pgType: 'text',
          label: 'UOM',
          alias: 'UOM',
        },
        {
          name: 'Scope',
          type: 'integer',
          pgType: 'bigint',
          label: 'Scope',
          alias: 'Scope',
        },
        {
          name: 'Completed',
          type: 'integer',
          pgType: 'bigint',
          label: 'Completed',
          alias: 'Completed',
        },
        {
          name: 'Actual Start',
          type: 'date',
          pgType: 'date',
          label: 'Actual Start',
          alias: 'Actual Start',
        },
        {
          name: 'Actual Finish',
          type: 'date',
          pgType: 'date',
          label: 'Actual Finish',
          alias: 'Actual Finish',
        },
        {
          name: 'Remaining',
          type: 'numeric',
          pgType: 'double precision',
          label: 'Remaining',
          alias: 'Remaining',
        },
        {
          name: '% Progress',
          type: 'integer',
          pgType: 'bigint',
          label: '% Progress',
          alias: '% Progress',
        },
        {
          name: 'Status',
          type: 'text',
          pgType: 'text',
          label: 'Status',
          alias: 'Status',
        },
        {
          name: 'Planned Start',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Start',
          alias: 'Planned Start',
        },
        {
          name: 'Planned Finish',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Finish',
          alias: 'Planned Finish',
        },
        {
          name: 'Expected Work rate',
          type: 'integer',
          pgType: 'bigint',
          label: 'Expected Work rate',
          alias: 'Expected Work rate',
        },
        {
          name: 'Est. Finish date',
          type: 'date',
          pgType: 'date',
          label: 'Est. Finish date',
          alias: 'Est. Finish date',
        },
        {
          name: 'Tags',
          type: 'unknown',
          pgType: 'ARRAY',
          label: 'Tags',
          alias: 'Tags',
        },
      ],
      columns_with_hierarchy: [
        {
          name: 'ID',
          type: 'text',
          pgType: 'text',
          label: 'ID',
          alias: 'ID',
        },
        {
          name: 'Activity',
          type: 'text',
          pgType: 'text',
          label: 'Activity',
          alias: 'Activity',
        },
        {
          name: 'Subactivity',
          type: 'text',
          pgType: 'text',
          label: 'Subactivity',
          alias: 'Subactivity',
        },
        {
          name: 'Layer',
          type: 'text',
          pgType: 'text',
          label: 'Layer',
          alias: 'Layer',
        },
        {
          name: 'Sublayer',
          type: 'text',
          pgType: 'text',
          label: 'Sublayer',
          alias: 'Sublayer',
        },
        {
          name: 'UOM',
          type: 'text',
          pgType: 'text',
          label: 'UOM',
          alias: 'UOM',
        },
        {
          name: 'Scope',
          type: 'integer',
          pgType: 'bigint',
          label: 'Scope',
          alias: 'Scope',
        },
        {
          name: 'Completed',
          type: 'integer',
          pgType: 'bigint',
          label: 'Completed',
          alias: 'Completed',
        },
        {
          name: 'Actual Start',
          type: 'date',
          pgType: 'date',
          label: 'Actual Start',
          alias: 'Actual Start',
        },
        {
          name: 'Actual Finish',
          type: 'date',
          pgType: 'date',
          label: 'Actual Finish',
          alias: 'Actual Finish',
        },
        {
          name: 'Remaining',
          type: 'numeric',
          pgType: 'double precision',
          label: 'Remaining',
          alias: 'Remaining',
        },
        {
          name: '% Progress',
          type: 'integer',
          pgType: 'bigint',
          label: '% Progress',
          alias: '% Progress',
        },
        {
          name: 'Status',
          type: 'text',
          pgType: 'text',
          label: 'Status',
          alias: 'Status',
        },
        {
          name: 'Planned Start',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Start',
          alias: 'Planned Start',
        },
        {
          name: 'Planned Finish',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Finish',
          alias: 'Planned Finish',
        },
        {
          name: 'Expected Work rate',
          type: 'integer',
          pgType: 'bigint',
          label: 'Expected Work rate',
          alias: 'Expected Work rate',
        },
        {
          name: 'Est. Finish date',
          type: 'date',
          pgType: 'date',
          label: 'Est. Finish date',
          alias: 'Est. Finish date',
        },
        {
          name: 'Tags',
          type: 'unknown',
          pgType: 'ARRAY',
          label: 'Tags',
          alias: 'Tags',
        },
      ],
    },
    value: {
      columns: [
        {
          name: 'Activity',
          type: 'text',
          pgType: 'text',
          label: 'Activity',
          alias: 'Activity',
          table_name: 'progress_data',
          is_table_column: true,
        },
        {
          name: 'Completed',
          type: 'integer',
          pgType: 'bigint',
          label: 'Completed',
          alias: 'Completed',
          table_name: 'progress_data',
          is_table_column: true,
        },
        {
          name: 'Scope',
          type: 'integer',
          pgType: 'bigint',
          label: 'Scope',
          alias: 'Scope',
          table_name: 'progress_data',
          is_table_column: true,
        },
      ],
      orderBy: [],
      filters: [],
      limit: null,
    },
    tables: [
      {
        label: 'progress_data',
        columns: [
          {
            name: 'ID',
            type: 'text',
            pgType: 'text',
            label: 'ID',
            alias: 'ID',
          },
          {
            name: 'Activity',
            type: 'text',
            pgType: 'text',
            label: 'Activity',
            alias: 'Activity',
          },
          {
            name: 'Subactivity',
            type: 'text',
            pgType: 'text',
            label: 'Subactivity',
            alias: 'Subactivity',
          },
          {
            name: 'Layer',
            type: 'text',
            pgType: 'text',
            label: 'Layer',
            alias: 'Layer',
          },
          {
            name: 'Sublayer',
            type: 'text',
            pgType: 'text',
            label: 'Sublayer',
            alias: 'Sublayer',
          },
          {
            name: 'UOM',
            type: 'text',
            pgType: 'text',
            label: 'UOM',
            alias: 'UOM',
          },
          {
            name: 'Scope',
            type: 'integer',
            pgType: 'bigint',
            label: 'Scope',
            alias: 'Scope',
          },
          {
            name: 'Completed',
            type: 'integer',
            pgType: 'bigint',
            label: 'Completed',
            alias: 'Completed',
          },
          {
            name: 'Actual Start',
            type: 'date',
            pgType: 'date',
            label: 'Actual Start',
            alias: 'Actual Start',
          },
          {
            name: 'Actual Finish',
            type: 'date',
            pgType: 'date',
            label: 'Actual Finish',
            alias: 'Actual Finish',
          },
          {
            name: 'Remaining',
            type: 'numeric',
            pgType: 'double precision',
            label: 'Remaining',
            alias: 'Remaining',
          },
          {
            name: '% Progress',
            type: 'integer',
            pgType: 'bigint',
            label: '% Progress',
            alias: '% Progress',
          },
          {
            name: 'Status',
            type: 'text',
            pgType: 'text',
            label: 'Status',
            alias: 'Status',
          },
          {
            name: 'Planned Start',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Start',
            alias: 'Planned Start',
          },
          {
            name: 'Planned Finish',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Finish',
            alias: 'Planned Finish',
          },
          {
            name: 'Expected Work rate',
            type: 'integer',
            pgType: 'bigint',
            label: 'Expected Work rate',
            alias: 'Expected Work rate',
          },
          {
            name: 'Est. Finish date',
            type: 'date',
            pgType: 'date',
            label: 'Est. Finish date',
            alias: 'Est. Finish date',
          },
          {
            name: 'Tags',
            type: 'unknown',
            pgType: 'ARRAY',
            label: 'Tags',
            alias: 'Tags',
          },
        ],
        columns_with_hierarchy: [
          {
            name: 'ID',
            type: 'text',
            pgType: 'text',
            label: 'ID',
            alias: 'ID',
          },
          {
            name: 'Activity',
            type: 'text',
            pgType: 'text',
            label: 'Activity',
            alias: 'Activity',
          },
          {
            name: 'Subactivity',
            type: 'text',
            pgType: 'text',
            label: 'Subactivity',
            alias: 'Subactivity',
          },
          {
            name: 'Layer',
            type: 'text',
            pgType: 'text',
            label: 'Layer',
            alias: 'Layer',
          },
          {
            name: 'Sublayer',
            type: 'text',
            pgType: 'text',
            label: 'Sublayer',
            alias: 'Sublayer',
          },
          {
            name: 'UOM',
            type: 'text',
            pgType: 'text',
            label: 'UOM',
            alias: 'UOM',
          },
          {
            name: 'Scope',
            type: 'integer',
            pgType: 'bigint',
            label: 'Scope',
            alias: 'Scope',
          },
          {
            name: 'Completed',
            type: 'integer',
            pgType: 'bigint',
            label: 'Completed',
            alias: 'Completed',
          },
          {
            name: 'Actual Start',
            type: 'date',
            pgType: 'date',
            label: 'Actual Start',
            alias: 'Actual Start',
          },
          {
            name: 'Actual Finish',
            type: 'date',
            pgType: 'date',
            label: 'Actual Finish',
            alias: 'Actual Finish',
          },
          {
            name: 'Remaining',
            type: 'numeric',
            pgType: 'double precision',
            label: 'Remaining',
            alias: 'Remaining',
          },
          {
            name: '% Progress',
            type: 'integer',
            pgType: 'bigint',
            label: '% Progress',
            alias: '% Progress',
          },
          {
            name: 'Status',
            type: 'text',
            pgType: 'text',
            label: 'Status',
            alias: 'Status',
          },
          {
            name: 'Planned Start',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Start',
            alias: 'Planned Start',
          },
          {
            name: 'Planned Finish',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Finish',
            alias: 'Planned Finish',
          },
          {
            name: 'Expected Work rate',
            type: 'integer',
            pgType: 'bigint',
            label: 'Expected Work rate',
            alias: 'Expected Work rate',
          },
          {
            name: 'Est. Finish date',
            type: 'date',
            pgType: 'date',
            label: 'Est. Finish date',
            alias: 'Est. Finish date',
          },
          {
            name: 'Tags',
            type: 'unknown',
            pgType: 'ARRAY',
            label: 'Tags',
            alias: 'Tags',
          },
        ],
      },
    ],
  },
];

export const TEMPORARY_GRID_DATA = {
  first_widget: {
    name: 'Widget 1',
    config: {
      chart: {
        type: 'column',
        data: {
          category: 'Activity',
          values: [
            'Completed',
          ],
          stackBy: null,
        },
        series: {
          Completed: {
            name: 'Completed',
            type: 'bar',
            yAxisIndex: 0,
            color: '#4778b8',
            lineColor: '#4778b8',
            lineWidth: 1,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: stages_one,
      },
    },
  },
  second_widget: {
    name: 'Widget 2',
    config: {
      chart: {
        type: 'line',
        data: {
          category: 'Activity',
          values: [
            'Completed',
          ],
          stackBy: null,
        },
        series: {
          Completed: {
            name: 'Completed',
            type: 'line',
            yAxisIndex: 0,
            color: '#4778b8',
            lineColor: '#4778b8',
            lineWidth: 1,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: stages_one,
      },
    },
  },
  third_widget: {
    name: 'Widget 3',
    config: {
      chart: {
        type: 'pie',
        data: {
          category: 'Activity',
          values: [
            'Completed',
          ],
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {},
        reference_lines: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: stages_one,
      },
    },
  },
  fourth_widget: {
    name: 'Widget 4',
    config: {
      chart: {
        type: 'funnel',
        data: {
          category: 'Activity',
          values: [
            'Completed',
          ],
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {},
        chartSpecific: {
          funnel: {
            labelsAtCenter: false,
            showPercentages: false,
            is3D: false,
            percentOfPrevious: false,
            maintainSlope: false,
          },
        },
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: stages_one,
      },
    },
  },
  fifth_widget: {
    name: 'Widget 5',
    config: {
      chart: {
        type: 'table',
        columns_map: {
          Activity: {
            key: 'Activity',
            width: null,
            visible: true,
            order_index: 0,
          },
          Scope: {
            key: 'Scope',
            width: null,
            visible: true,
            order_index: 1,
          },
          Completed: {
            key: 'Completed',
            width: null,
            visible: true,
            order_index: 3,
          },
        },
        show_row_headers: false,
        conditional_formatting: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: stages_one,
      },
    },
  },
};

export const DASHBOARDS = [
  {
    uid: '1',
    name: 'Dashboard 1',
    widgets: [
      { x: 0, y: 0, w: 10, h: 15, i: '0', widget_id: 'first_widget' },
      { x: 0, y: 15, w: 8, h: 10, i: '1', widget_id: 'second_widget' },
      { x: 2, y: 25, w: 4, h: 10, i: '2', widget_id: 'third_widget' },
      { x: 3, y: 35, w: 4, h: 10, i: '3', widget_id: 'fourth_widget' },
      { x: 3, y: 35, w: 4, h: 10, i: '4', widget_id: 'fifth_widget' },
    ],
  },
  {
    uid: '2',
    name: 'Dashboard 2',
    widgets: [
      { x: 0, y: 0, w: 10, h: 10, i: '1', widget_id: 'second_widget' },
      { x: 0, y: 10, w: 10, h: 15, i: '0', widget_id: 'third_widget' },
    ],
  },
];
