import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();

export const useBiStore = defineStore('bi', {
  state: () => ({
    dashboards: {},
    selected_dashboard: null,
    all_widget_details: {},
    all_tables: [],
    config_change_detection_counter: 1,
    widget_data: [],
    table_metadata: {},
    selected_stage_index: null,
    are_chart_builder_fields_filled: false,
    is_table_dirty: false,
    widget_builder_config: {
      query: {
        selected_table: null,
        sql_query: '',
        selected_database: 'grs_corvera',
        stages: [],
      },
      chart: {},
    },
    chart_builder_data_types: {},
  }),
  getters: {
    selected_dashboard_details() {
      return this.dashboards[this.selected_dashboard];
    },
  },
  actions: {
    set_chart_builder_data_types() {
      const alias_to_column_mapping = this.alias_to_column_mapping();
      this.chart_builder_data_types = Object.keys(alias_to_column_mapping).reduce((acc, key) => {
        let type = alias_to_column_mapping[key].type;
        switch (alias_to_column_mapping[key].type) {
          case 'string':
          case 'text':
          case 'text_array':
            type = 'text';
            break;
          case 'numeric':
          case 'integer':
          case 'decimal':
          case 'float':
            type = 'numeric';
            break;
          case 'date':
          case 'time':
          case 'timestamp':
            type = 'date';
            break;
          case 'boolean':
            type = 'boolean';
            break;
        }
        acc[key] = type;
        return acc;
      }, {});
    },
    alias_to_column_mapping() {
      if (!this.widget_builder_config.query.stages.length && !this.widget_builder_config.query.stages?.[0]?.value?.columns?.length)
        return {};
      const column_mapping = {};
      this.widget_builder_config.query.stages.forEach(stage => stage.value.columns.forEach((column) => {
        column_mapping[column.alias] = column;
      }));
      return column_mapping;
    },
    async reset_widget_builder() {
      this.widget_builder_config = {
        query: {
          selected_table: null,
          sql_query: '',
          selected_database: 'grs_corvera',
          stages: [],
        },
        chart: {},
      };
      this.widget_data = [];
    },
    async getTables() {
      const response = await this.$services.bi_schema.table({
        id: this.widget_builder_config.query.selected_database,
      });
      this.all_tables = response.data.tables.map(table => ({ label: table.name, columns: table.columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ table_name: table.name, column_name: column.name }) })) }));
      return response.data;
    },
    async getTableColumns({ table_name }) {
      const response = await this.$services.bi_schema.columns({
        id: this.widget_builder_config.query.selected_database,
        table_name,
      });
      return response.data;
    },
    async selectTable(table) {
      let { columns } = await this.getTableColumns({ table_name: table.label });
      columns = columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ column_name: column.name }) }));
      const columns_with_hierarchy = bi_query_builder.buildHierarchy(columns);
      this.widget_builder_config.query.selected_table = {
        label: table.label,
        columns,
        columns_with_hierarchy,
      };
    },
    async getWidgetData(selected_database, stages) {
      const response = await this.$services.bi_query.execute({
        id: selected_database,
        body: bi_query_builder.getStageConfig(stages),
      });
      return response.data;
    },
    async getColumnValues({ table_name, column_name, query }) {
      const response = await this.$services.bi_schema.column_values({
        id: this.widget_builder_config.query.selected_database,
        table_name,
        column_name,
        query,
      });
      return response.data;
    },
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
